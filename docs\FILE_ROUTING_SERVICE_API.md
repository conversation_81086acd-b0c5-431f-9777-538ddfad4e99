# FileRoutingService API Documentation

## Getting Started

### Step 1: Create API Key

Before using the FileRoutingService API, you must create an API key through the ChatLo Admin Dashboard:

1. **Access Admin Dashboard**: Navigate to ChatLo → Settings → Admin Dashboard
2. **Go to API Management**: Click on "API Keys" in the left sidebar
3. **Create New Service**: Click "Create New API Key" button
4. **Fill Service Details**:
   ```
   Service Name: Your Application Name
   Version: 1.0.0
   Description: Brief description of your service
   Developer Name: Your Name
   Developer Email: <EMAIL>
   Organization: Your Company (optional)
   Requested Capabilities: Select from available options
   ```
5. **Generate Key**: Click "Generate API Key"
6. **Save Credentials**: Copy and securely store your:
   - Service ID: `srv_xxxxxxxxxx`
   - API Key: `ak_xxxxxxxxxx`

⚠️ **Important**: API keys are only shown once. Store them securely as they cannot be retrieved later.

### Step 2: Test Connection

Use the provided code examples below to test your API key and establish connection.

### Admin Dashboard Integration

The ChatLo Admin Dashboard provides comprehensive API key management:

**Navigation**: ChatLo → Settings → Admin Dashboard → API Keys

**Features**:
- **Create API Keys**: Full service registration with capability selection
- **Monitor Usage**: Real-time statistics and success rates
- **Access Control**: Suspend or revoke services instantly
- **Audit Trail**: Complete history of all API key actions
- **Security Dashboard**: Monitor failed attempts and suspicious activity

**Audit Logging**: Every API key action is automatically logged:
- API key creation with developer details
- All API access attempts (successful and failed)
- Service status changes (suspend/revoke/reactivate)
- Admin actions with IP addresses and timestamps

This ensures full compliance and security monitoring for enterprise environments.

## Overview

The FileRoutingService API provides a secure, token-based interface for file operations within ChatLo. It serves as the central hub for all file handling operations, replacing the complex SharedDropboxService with a unified, extensible architecture.

**All API keys and access are automatically logged and monitored through the ChatLo Admin Dashboard for security and compliance.**

### Core Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client App    │───▶│ Token Manager   │───▶│ Service Registry│
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│FileRoutingService│───▶│ Access Control  │───▶│   Audit Log     │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐
│ Electron IPC    │
│ (File System)   │
└─────────────────┘
```

## Data Flow Chart

### File Upload Flow
```
1. Client Request
   ├─ Token Validation
   ├─ File Validation
   └─ Route Resolution
       │
2. Processing Pipeline
   ├─ Auto-routing Logic
   ├─ Context Resolution
   └─ Path Generation
       │
3. File Operations
   ├─ IPC Communication
   ├─ File System Write
   └─ Metadata Extraction
       │
4. Response & Logging
   ├─ Success/Error Response
   ├─ Audit Log Entry
   └─ Metrics Recording
```

### Authentication Flow
```
1. Service Registration
   ├─ Generate Service ID
   ├─ Create API Key
   └─ Register in Service Registry
       │
2. Token Request (Handshake)
   ├─ Service ID + API Key
   ├─ Capability Request
   └─ Token Generation
       │
3. API Access
   ├─ Token Validation
   ├─ Permission Check
   └─ Operation Execution
       │
4. Token Refresh
   ├─ Refresh Token
   ├─ New Access Token
   └─ Updated Permissions
```

## Standard Protocol

### Authentication Protocol

#### 1. Service Registration
```http
POST /api/v1/services/register
Content-Type: application/json

{
  "serviceName": "MyFileProcessor",
  "version": "1.0.0",
  "capabilities": ["file.upload", "file.read", "file.process"],
  "description": "Custom file processing service",
  "developer": {
    "name": "Developer Name",
    "email": "<EMAIL>",
    "organization": "Company Name"
  }
}

Response:
{
  "success": true,
  "serviceId": "srv_1234567890abcdef",
  "apiKey": "ak_abcdef1234567890",
  "registeredAt": "2024-01-15T10:30:00Z"
}
```

#### 2. Token Handshake
```http
POST /api/v1/auth/handshake
Content-Type: application/json
Authorization: Bearer ak_abcdef1234567890

{
  "serviceId": "srv_1234567890abcdef",
  "requestedCapabilities": ["file.upload", "file.read"],
  "contextId": "ctx_optional_context",
  "sessionInfo": {
    "userAgent": "MyApp/1.0.0",
    "platform": "electron",
    "version": "1.0.0"
  }
}

Response:
{
  "success": true,
  "accessToken": "at_xyz789abc123def456",
  "refreshToken": "rt_def456ghi789jkl012",
  "expiresIn": 3600,
  "capabilities": ["file.upload", "file.read"],
  "tokenType": "Bearer"
}
```

#### 3. Token Refresh
```http
POST /api/v1/auth/refresh
Content-Type: application/json

{
  "refreshToken": "rt_def456ghi789jkl012",
  "serviceId": "srv_1234567890abcdef"
}

Response:
{
  "success": true,
  "accessToken": "at_new789abc123def456",
  "expiresIn": 3600
}
```

## API Endpoints (Version 1.0)

### File Operations

#### Upload Files
```http
POST /api/v1/files/upload
Authorization: Bearer at_xyz789abc123def456
Content-Type: multipart/form-data

Form Data:
- files: File[] (required)
- destination: JSON string (required)
- options: JSON string (optional)

Example destination:
{
  "contextId": "ctx_123456",
  "path": "/custom/path",
  "autoRoute": true
}

Response:
{
  "success": true,
  "files": [
    {
      "id": "file_1234567890",
      "originalName": "document.pdf",
      "savedPath": "/contexts/ctx_123456/documents/document.pdf",
      "size": 1024000,
      "mimeType": "application/pdf",
      "contextId": "ctx_123456",
      "metadata": {
        "pages": 10,
        "author": "John Doe"
      }
    }
  ],
  "uploadId": "upload_abc123def456"
}
```

#### Read File
```http
GET /api/v1/files/read
Authorization: Bearer at_xyz789abc123def456
Query Parameters:
- filePath: string (required)
- encoding: string (optional, default: utf8)
- range: string (optional, format: "start-end")

Response:
{
  "success": true,
  "content": "file content or base64 for binary",
  "metadata": {
    "size": 1024,
    "mimeType": "text/plain",
    "modified": "2024-01-15T10:30:00Z"
  }
}
```

#### List Files
```http
GET /api/v1/files/list
Authorization: Bearer at_xyz789abc123def456
Query Parameters:
- path: string (required)
- recursive: boolean (optional, default: false)
- filter: JSON string (optional)

Example filter:
{
  "extensions": [".pdf", ".txt"],
  "maxSize": 10485760,
  "mimeTypes": ["text/*"]
}

Response:
{
  "success": true,
  "files": [
    {
      "name": "document.pdf",
      "path": "/contexts/ctx_123456/documents/document.pdf",
      "size": 1024000,
      "mimeType": "application/pdf",
      "isDirectory": false,
      "modified": "2024-01-15T10:30:00Z"
    }
  ],
  "totalCount": 1,
  "hasMore": false
}
```

#### Delete Files
```http
DELETE /api/v1/files/delete
Authorization: Bearer at_xyz789abc123def456
Content-Type: application/json

{
  "filePaths": [
    "/contexts/ctx_123456/documents/document.pdf"
  ],
  "options": {
    "moveToTrash": true,
    "force": false
  }
}

Response:
{
  "success": true,
  "deletedFiles": [
    "/contexts/ctx_123456/documents/document.pdf"
  ],
  "errors": []
}
```

### Batch Operations
```http
POST /api/v1/files/batch
Authorization: Bearer at_xyz789abc123def456
Content-Type: application/json

{
  "operations": [
    {
      "type": "upload",
      "files": ["file1.txt", "file2.pdf"],
      "destination": {
        "contextId": "ctx_123456",
        "autoRoute": true
      }
    },
    {
      "type": "move",
      "source": "/old/path/file.txt",
      "destination": "/new/path/file.txt"
    }
  ]
}

Response:
{
  "success": true,
  "results": [
    {
      "operation": {...},
      "success": true,
      "result": {...}
    }
  ]
}
```

### System Operations

#### Get Service Info
```http
GET /api/v1/services/info
Authorization: Bearer at_xyz789abc123def456

Response:
{
  "success": true,
  "service": {
    "id": "srv_1234567890abcdef",
    "name": "MyFileProcessor",
    "version": "1.0.0",
    "capabilities": ["file.upload", "file.read"],
    "registeredAt": "2024-01-15T10:30:00Z",
    "lastAccess": "2024-01-15T11:45:00Z"
  }
}
```

#### Get Usage Statistics
```http
GET /api/v1/services/stats
Authorization: Bearer at_xyz789abc123def456

Response:
{
  "success": true,
  "stats": {
    "totalOperations": 150,
    "totalSize": ********,
    "totalErrors": 2,
    "errorRate": 0.013,
    "lastReset": "2024-01-01T00:00:00Z"
  }
}
```

## Code Examples

### JavaScript/TypeScript Client

```typescript
// FileRoutingClient.ts
export class FileRoutingClient {
  private accessToken: string | null = null
  private refreshToken: string | null = null
  private baseURL = 'http://localhost:3001/api/v1'

  constructor(
    private serviceId: string,
    private apiKey: string
  ) {}

  async authenticate(capabilities: string[]): Promise<void> {
    const response = await fetch(`${this.baseURL}/auth/handshake`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      },
      body: JSON.stringify({
        serviceId: this.serviceId,
        requestedCapabilities: capabilities,
        sessionInfo: {
          userAgent: 'FileRoutingClient/1.0.0',
          platform: 'node',
          version: '1.0.0'
        }
      })
    })

    const data = await response.json()
    if (data.success) {
      this.accessToken = data.accessToken
      this.refreshToken = data.refreshToken
    } else {
      throw new Error(`Authentication failed: ${data.error}`)
    }
  }

  async uploadFiles(files: File[], destination: any): Promise<any> {
    if (!this.accessToken) {
      throw new Error('Not authenticated')
    }

    const formData = new FormData()
    files.forEach(file => formData.append('files', file))
    formData.append('destination', JSON.stringify(destination))

    const response = await fetch(`${this.baseURL}/files/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.accessToken}`
      },
      body: formData
    })

    return await response.json()
  }

  async readFile(filePath: string): Promise<any> {
    if (!this.accessToken) {
      throw new Error('Not authenticated')
    }

    const response = await fetch(
      `${this.baseURL}/files/read?filePath=${encodeURIComponent(filePath)}`,
      {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`
        }
      }
    )

    return await response.json()
  }

  async listFiles(path: string, options: any = {}): Promise<any> {
    if (!this.accessToken) {
      throw new Error('Not authenticated')
    }

    const params = new URLSearchParams({
      path,
      ...options
    })

    const response = await fetch(`${this.baseURL}/files/list?${params}`, {
      headers: {
        'Authorization': `Bearer ${this.accessToken}`
      }
    })

    return await response.json()
  }

  private async refreshAccessToken(): Promise<void> {
    if (!this.refreshToken) {
      throw new Error('No refresh token available')
    }

    const response = await fetch(`${this.baseURL}/auth/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        refreshToken: this.refreshToken,
        serviceId: this.serviceId
      })
    })

    const data = await response.json()
    if (data.success) {
      this.accessToken = data.accessToken
    } else {
      throw new Error(`Token refresh failed: ${data.error}`)
    }
  }
}

// Usage Example
const client = new FileRoutingClient(
  'srv_1234567890abcdef',
  'ak_abcdef1234567890'
)

async function example() {
  // Authenticate
  await client.authenticate(['file.upload', 'file.read', 'file.list'])

  // Upload files
  const files = [/* File objects */]
  const uploadResult = await client.uploadFiles(files, {
    contextId: 'ctx_123456',
    autoRoute: true
  })

  console.log('Upload result:', uploadResult)

  // List files
  const fileList = await client.listFiles('/contexts/ctx_123456')
  console.log('Files:', fileList.files)

  // Read file
  const fileContent = await client.readFile('/contexts/ctx_123456/documents/test.txt')
  console.log('Content:', fileContent.content)
}
```

### Python Client

```python
# file_routing_client.py
import requests
import json
from typing import List, Dict, Any, Optional

class FileRoutingClient:
    def __init__(self, service_id: str, api_key: str, base_url: str = "http://localhost:3001/api/v1"):
        self.service_id = service_id
        self.api_key = api_key
        self.base_url = base_url
        self.access_token: Optional[str] = None
        self.refresh_token: Optional[str] = None

    def authenticate(self, capabilities: List[str]) -> None:
        """Authenticate and get access token"""
        response = requests.post(
            f"{self.base_url}/auth/handshake",
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            },
            json={
                "serviceId": self.service_id,
                "requestedCapabilities": capabilities,
                "sessionInfo": {
                    "userAgent": "FileRoutingClient-Python/1.0.0",
                    "platform": "python",
                    "version": "1.0.0"
                }
            }
        )
        
        data = response.json()
        if data["success"]:
            self.access_token = data["accessToken"]
            self.refresh_token = data["refreshToken"]
        else:
            raise Exception(f"Authentication failed: {data.get('error')}")

    def upload_files(self, file_paths: List[str], destination: Dict[str, Any]) -> Dict[str, Any]:
        """Upload files to the service"""
        if not self.access_token:
            raise Exception("Not authenticated")

        files = []
        for file_path in file_paths:
            files.append(('files', open(file_path, 'rb')))

        data = {
            'destination': json.dumps(destination)
        }

        response = requests.post(
            f"{self.base_url}/files/upload",
            headers={
                "Authorization": f"Bearer {self.access_token}"
            },
            files=files,
            data=data
        )

        # Close file handles
        for _, file_handle in files:
            file_handle.close()

        return response.json()

    def read_file(self, file_path: str) -> Dict[str, Any]:
        """Read file content"""
        if not self.access_token:
            raise Exception("Not authenticated")

        response = requests.get(
            f"{self.base_url}/files/read",
            headers={
                "Authorization": f"Bearer {self.access_token}"
            },
            params={"filePath": file_path}
        )

        return response.json()

    def list_files(self, path: str, **options) -> Dict[str, Any]:
        """List files in directory"""
        if not self.access_token:
            raise Exception("Not authenticated")

        params = {"path": path, **options}
        response = requests.get(
            f"{self.base_url}/files/list",
            headers={
                "Authorization": f"Bearer {self.access_token}"
            },
            params=params
        )

        return response.json()

# Usage Example
if __name__ == "__main__":
    client = FileRoutingClient(
        service_id="srv_1234567890abcdef",
        api_key="ak_abcdef1234567890"
    )

    # Authenticate
    client.authenticate(["file.upload", "file.read", "file.list"])

    # Upload files
    upload_result = client.upload_files(
        file_paths=["./test1.txt", "./test2.pdf"],
        destination={
            "contextId": "ctx_123456",
            "autoRoute": True
        }
    )
    print("Upload result:", upload_result)

    # List files
    file_list = client.list_files("/contexts/ctx_123456")
    print("Files:", file_list["files"])

    # Read file
    file_content = client.read_file("/contexts/ctx_123456/documents/test1.txt")
    print("Content:", file_content["content"])
```

## Service Access Logging System

### Database Schema

```sql
-- Service Registry Table (Enhanced for Admin Dashboard)
CREATE TABLE service_registry (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    version VARCHAR(50) NOT NULL,
    description TEXT NULL,
    api_key_hash VARCHAR(255) NOT NULL,
    api_key_preview VARCHAR(20) NOT NULL, -- First 4 and last 4 chars for display
    capabilities JSON NOT NULL,
    developer_info JSON NOT NULL,
    status ENUM('active', 'suspended', 'revoked') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100) DEFAULT 'admin', -- Who created the API key
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_access TIMESTAMP NULL,
    revoked_at TIMESTAMP NULL,
    revoked_by VARCHAR(100) NULL,
    revoked_reason TEXT NULL,
    INDEX idx_name (name),
    INDEX idx_status (status),
    INDEX idx_last_access (last_access),
    INDEX idx_created_by (created_by)
);

-- Access Tokens Table
CREATE TABLE access_tokens (
    token_id VARCHAR(50) PRIMARY KEY,
    service_id VARCHAR(50) NOT NULL,
    access_token_hash VARCHAR(255) NOT NULL,
    refresh_token_hash VARCHAR(255) NOT NULL,
    capabilities JSON NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP NULL,
    context_id VARCHAR(50) NULL,
    status ENUM('active', 'expired', 'revoked') DEFAULT 'active',
    FOREIGN KEY (service_id) REFERENCES service_registry(id) ON DELETE CASCADE,
    INDEX idx_service_id (service_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_status (status)
);

-- API Access Log Table
CREATE TABLE api_access_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    service_id VARCHAR(50) NOT NULL,
    token_id VARCHAR(50) NULL,
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    status_code INT NOT NULL,
    response_time_ms INT NOT NULL,
    request_size BIGINT DEFAULT 0,
    response_size BIGINT DEFAULT 0,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    error_message TEXT NULL,
    context_id VARCHAR(50) NULL,
    file_paths JSON NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (service_id) REFERENCES service_registry(id) ON DELETE CASCADE,
    FOREIGN KEY (token_id) REFERENCES access_tokens(token_id) ON DELETE SET NULL,
    INDEX idx_service_id (service_id),
    INDEX idx_timestamp (timestamp),
    INDEX idx_endpoint (endpoint),
    INDEX idx_status_code (status_code)
);

-- Usage Statistics Table
CREATE TABLE usage_statistics (
    service_id VARCHAR(50) NOT NULL,
    date DATE NOT NULL,
    total_requests INT DEFAULT 0,
    successful_requests INT DEFAULT 0,
    failed_requests INT DEFAULT 0,
    total_bytes_uploaded BIGINT DEFAULT 0,
    total_bytes_downloaded BIGINT DEFAULT 0,
    avg_response_time_ms DECIMAL(10,2) DEFAULT 0,
    unique_contexts INT DEFAULT 0,
    PRIMARY KEY (service_id, date),
    FOREIGN KEY (service_id) REFERENCES service_registry(id) ON DELETE CASCADE,
    INDEX idx_date (date)
);

-- API Key Audit Log Table (For Admin Dashboard)
CREATE TABLE api_key_audit_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    service_id VARCHAR(50) NOT NULL,
    action ENUM('created', 'accessed', 'suspended', 'revoked', 'reactivated') NOT NULL,
    performed_by VARCHAR(100) NOT NULL, -- Admin username or system
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    details JSON NULL, -- Additional context (reason, old values, etc.)
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (service_id) REFERENCES service_registry(id) ON DELETE CASCADE,
    INDEX idx_service_id (service_id),
    INDEX idx_action (action),
    INDEX idx_timestamp (timestamp),
    INDEX idx_performed_by (performed_by)
);
```

### Service Access Logger Implementation

```typescript
// src/services/serviceAccessLogger.ts
import { BaseService, ServiceError, ServiceErrorCode } from './base'

export interface ServiceRegistration {
  id: string
  name: string
  version: string
  apiKeyHash: string
  capabilities: string[]
  developerInfo: {
    name: string
    email: string
    organization?: string
  }
  status: 'active' | 'suspended' | 'revoked'
  createdAt: Date
  lastAccess?: Date
}

export interface AccessToken {
  tokenId: string
  serviceId: string
  accessTokenHash: string
  refreshTokenHash: string
  capabilities: string[]
  expiresAt: Date
  contextId?: string
  status: 'active' | 'expired' | 'revoked'
}

export interface AccessLogEntry {
  serviceId: string
  tokenId?: string
  endpoint: string
  method: string
  statusCode: number
  responseTimeMs: number
  requestSize: number
  responseSize: number
  ipAddress?: string
  userAgent?: string
  errorMessage?: string
  contextId?: string
  filePaths?: string[]
  timestamp: Date
}

export interface UsageStats {
  serviceId: string
  date: string
  totalRequests: number
  successfulRequests: number
  failedRequests: number
  totalBytesUploaded: number
  totalBytesDownloaded: number
  avgResponseTimeMs: number
  uniqueContexts: number
}

export class ServiceAccessLogger extends BaseService {
  constructor() {
    super({
      name: 'ServiceAccessLogger',
      autoInitialize: true
    })
  }

  protected async doInitialize(): Promise<void> {
    // Initialize database connection
    await this.initializeDatabase()
    this.logger.info('ServiceAccessLogger initialized', 'doInitialize')
  }

  /**
   * Register a new service with full audit logging
   */
  async registerService(
    registration: Omit<ServiceRegistration, 'id' | 'createdAt'>,
    createdBy: string = 'admin',
    ipAddress?: string,
    userAgent?: string
  ): Promise<{ serviceId: string; apiKey: string }> {
    return await this.executeOperationOrThrow(
      'registerService',
      async () => {
        const serviceId = this.generateServiceId()
        const apiKey = this.generateAPIKey()
        const apiKeyHash = await this.hashAPIKey(apiKey)
        const apiKeyPreview = this.createAPIKeyPreview(apiKey)
        const now = new Date()

        // Insert service registration
        const serviceQuery = `
          INSERT INTO service_registry
          (id, name, version, description, api_key_hash, api_key_preview, capabilities,
           developer_info, status, created_at, created_by)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `

        await this.executeQuery(serviceQuery, [
          serviceId,
          registration.name,
          registration.version,
          registration.description || null,
          apiKeyHash,
          apiKeyPreview,
          JSON.stringify(registration.capabilities),
          JSON.stringify(registration.developerInfo),
          registration.status,
          now,
          createdBy
        ])

        // Log the API key creation in audit log
        await this.logAPIKeyAction(serviceId, 'created', createdBy, ipAddress, userAgent, {
          serviceName: registration.name,
          version: registration.version,
          capabilities: registration.capabilities,
          developerInfo: registration.developerInfo
        })

        this.logger.info(`Service registered: ${registration.name}`, 'registerService', {
          serviceId,
          capabilities: registration.capabilities,
          createdBy
        })

        return { serviceId, apiKey }
      },
      { serviceName: registration.name, createdBy }
    )
  }

  /**
   * Log API key actions for audit trail
   */
  async logAPIKeyAction(
    serviceId: string,
    action: 'created' | 'accessed' | 'suspended' | 'revoked' | 'reactivated',
    performedBy: string,
    ipAddress?: string,
    userAgent?: string,
    details?: any
  ): Promise<void> {
    const query = `
      INSERT INTO api_key_audit_log
      (service_id, action, performed_by, ip_address, user_agent, details, timestamp)
      VALUES (?, ?, ?, ?, ?, ?, NOW())
    `

    await this.executeQuery(query, [
      serviceId,
      action,
      performedBy,
      ipAddress,
      userAgent,
      details ? JSON.stringify(details) : null
    ])
  }

  /**
   * Create access token
   */
  async createAccessToken(tokenData: Omit<AccessToken, 'tokenId'>): Promise<string> {
    return await this.executeOperationOrThrow(
      'createAccessToken',
      async () => {
        const tokenId = this.generateTokenId()
        const now = new Date()

        const query = `
          INSERT INTO access_tokens
          (token_id, service_id, access_token_hash, refresh_token_hash, capabilities, expires_at, context_id, created_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `

        await this.executeQuery(query, [
          tokenId,
          tokenData.serviceId,
          tokenData.accessTokenHash,
          tokenData.refreshTokenHash,
          JSON.stringify(tokenData.capabilities),
          tokenData.expiresAt,
          tokenData.contextId,
          now
        ])

        return tokenId
      },
      { serviceId: tokenData.serviceId }
    )
  }

  /**
   * Log API access
   */
  async logAccess(entry: AccessLogEntry): Promise<void> {
    return await this.executeOperationOrThrow(
      'logAccess',
      async () => {
        const query = `
          INSERT INTO api_access_log
          (service_id, token_id, endpoint, method, status_code, response_time_ms,
           request_size, response_size, ip_address, user_agent, error_message,
           context_id, file_paths, timestamp)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `

        await this.executeQuery(query, [
          entry.serviceId,
          entry.tokenId,
          entry.endpoint,
          entry.method,
          entry.statusCode,
          entry.responseTimeMs,
          entry.requestSize,
          entry.responseSize,
          entry.ipAddress,
          entry.userAgent,
          entry.errorMessage,
          entry.contextId,
          entry.filePaths ? JSON.stringify(entry.filePaths) : null,
          entry.timestamp
        ])

        // Update last access time for service
        await this.updateLastAccess(entry.serviceId)

        // Update usage statistics
        await this.updateUsageStats(entry)
      },
      { serviceId: entry.serviceId, endpoint: entry.endpoint }
    )
  }

  /**
   * Get service access report
   */
  async getServiceAccessReport(serviceId?: string, days: number = 30): Promise<any[]> {
    return await this.executeOperationOrThrow(
      'getServiceAccessReport',
      async () => {
        const whereClause = serviceId ? 'WHERE sr.id = ?' : ''
        const params = serviceId ? [serviceId, days] : [days]

        const query = `
          SELECT
            sr.id,
            sr.name,
            sr.version,
            sr.status,
            sr.created_at,
            sr.last_access,
            COUNT(DISTINCT at.token_id) as active_tokens,
            COUNT(al.id) as total_requests,
            SUM(CASE WHEN al.status_code < 400 THEN 1 ELSE 0 END) as successful_requests,
            SUM(CASE WHEN al.status_code >= 400 THEN 1 ELSE 0 END) as failed_requests,
            AVG(al.response_time_ms) as avg_response_time,
            SUM(al.request_size) as total_bytes_uploaded,
            SUM(al.response_size) as total_bytes_downloaded
          FROM service_registry sr
          LEFT JOIN access_tokens at ON sr.id = at.service_id AND at.status = 'active'
          LEFT JOIN api_access_log al ON sr.id = al.service_id
            AND al.timestamp >= DATE_SUB(NOW(), INTERVAL ? DAY)
          ${whereClause}
          GROUP BY sr.id, sr.name, sr.version, sr.status, sr.created_at, sr.last_access
          ORDER BY sr.last_access DESC
        `

        const results = await this.executeQuery(query, params)
        return results
      },
      { serviceId, days }
    )
  }

  /**
   * Get detailed access logs
   */
  async getAccessLogs(
    serviceId?: string,
    startDate?: Date,
    endDate?: Date,
    limit: number = 1000
  ): Promise<AccessLogEntry[]> {
    return await this.executeOperationOrThrow(
      'getAccessLogs',
      async () => {
        let whereConditions = []
        let params: any[] = []

        if (serviceId) {
          whereConditions.push('service_id = ?')
          params.push(serviceId)
        }

        if (startDate) {
          whereConditions.push('timestamp >= ?')
          params.push(startDate)
        }

        if (endDate) {
          whereConditions.push('timestamp <= ?')
          params.push(endDate)
        }

        params.push(limit)

        const whereClause = whereConditions.length > 0
          ? `WHERE ${whereConditions.join(' AND ')}`
          : ''

        const query = `
          SELECT * FROM api_access_log
          ${whereClause}
          ORDER BY timestamp DESC
          LIMIT ?
        `

        const results = await this.executeQuery(query, params)
        return results.map(this.mapAccessLogEntry)
      },
      { serviceId, startDate, endDate, limit }
    )
  }

  /**
   * Revoke service access with audit logging
   */
  async revokeService(
    serviceId: string,
    reason?: string,
    revokedBy: string = 'admin',
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    return await this.executeOperationOrThrow(
      'revokeService',
      async () => {
        const now = new Date()

        // Update service status with revocation details
        await this.executeQuery(
          `UPDATE service_registry
           SET status = ?, updated_at = ?, revoked_at = ?, revoked_by = ?, revoked_reason = ?
           WHERE id = ?`,
          ['revoked', now, now, revokedBy, reason, serviceId]
        )

        // Revoke all active tokens
        await this.executeQuery(
          'UPDATE access_tokens SET status = ? WHERE service_id = ? AND status = ?',
          ['revoked', serviceId, 'active']
        )

        // Log the revocation in audit log
        await this.logAPIKeyAction(serviceId, 'revoked', revokedBy, ipAddress, userAgent, {
          reason: reason || 'No reason provided',
          revokedAt: now.toISOString()
        })

        this.logger.info(`Service revoked: ${serviceId}`, 'revokeService', {
          reason,
          revokedBy
        })
      },
      { serviceId, reason, revokedBy }
    )
  }

  /**
   * Get API key audit history for admin dashboard
   */
  async getAPIKeyAuditHistory(serviceId?: string, limit: number = 100): Promise<any[]> {
    return await this.executeOperationOrThrow(
      'getAPIKeyAuditHistory',
      async () => {
        const whereClause = serviceId ? 'WHERE aal.service_id = ?' : ''
        const params = serviceId ? [serviceId, limit] : [limit]

        const query = `
          SELECT
            aal.*,
            sr.name as service_name,
            sr.version as service_version
          FROM api_key_audit_log aal
          LEFT JOIN service_registry sr ON aal.service_id = sr.id
          ${whereClause}
          ORDER BY aal.timestamp DESC
          LIMIT ?
        `

        const results = await this.executeQuery(query, params)
        return results
      },
      { serviceId, limit }
    )
  }

  /**
   * Get admin dashboard statistics
   */
  async getAdminDashboardStats(): Promise<any> {
    return await this.executeOperationOrThrow(
      'getAdminDashboardStats',
      async () => {
        // Get service counts by status
        const serviceStats = await this.executeQuery(`
          SELECT
            status,
            COUNT(*) as count
          FROM service_registry
          GROUP BY status
        `, [])

        // Get recent API activity (last 24 hours)
        const recentActivity = await this.executeQuery(`
          SELECT
            COUNT(*) as total_requests,
            SUM(CASE WHEN status_code < 400 THEN 1 ELSE 0 END) as successful_requests,
            AVG(response_time_ms) as avg_response_time,
            SUM(request_size + response_size) as total_bytes
          FROM api_access_log
          WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        `, [])

        // Get recent audit actions
        const recentAuditActions = await this.executeQuery(`
          SELECT
            aal.action,
            aal.performed_by,
            aal.timestamp,
            sr.name as service_name,
            sr.version as service_version
          FROM api_key_audit_log aal
          LEFT JOIN service_registry sr ON aal.service_id = sr.id
          ORDER BY aal.timestamp DESC
          LIMIT 10
        `, [])

        return {
          serviceStats: serviceStats.reduce((acc: any, stat: any) => {
            acc[stat.status] = stat.count
            return acc
          }, {}),
          recentActivity: recentActivity[0] || {},
          recentAuditActions
        }
      },
      {}
    )
  }

  private async updateLastAccess(serviceId: string): Promise<void> {
    await this.executeQuery(
      'UPDATE service_registry SET last_access = NOW() WHERE id = ?',
      [serviceId]
    )
  }

  private async updateUsageStats(entry: AccessLogEntry): Promise<void> {
    const date = entry.timestamp.toISOString().split('T')[0]

    const query = `
      INSERT INTO usage_statistics
      (service_id, date, total_requests, successful_requests, failed_requests,
       total_bytes_uploaded, total_bytes_downloaded, avg_response_time_ms, unique_contexts)
      VALUES (?, ?, 1, ?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        total_requests = total_requests + 1,
        successful_requests = successful_requests + ?,
        failed_requests = failed_requests + ?,
        total_bytes_uploaded = total_bytes_uploaded + ?,
        total_bytes_downloaded = total_bytes_downloaded + ?,
        avg_response_time_ms = (avg_response_time_ms * (total_requests - 1) + ?) / total_requests,
        unique_contexts = GREATEST(unique_contexts, ?)
    `

    const isSuccess = entry.statusCode < 400 ? 1 : 0
    const isFailed = entry.statusCode >= 400 ? 1 : 0
    const contextCount = entry.contextId ? 1 : 0

    await this.executeQuery(query, [
      entry.serviceId,
      date,
      isSuccess,
      isFailed,
      entry.requestSize,
      entry.responseSize,
      entry.responseTimeMs,
      contextCount,
      isSuccess,
      isFailed,
      entry.requestSize,
      entry.responseSize,
      entry.responseTimeMs,
      contextCount
    ])
  }

  private generateServiceId(): string {
    return `srv_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  private generateTokenId(): string {
    return `tok_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  private generateAPIKey(): string {
    return `ak_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`
  }

  private async hashAPIKey(apiKey: string): Promise<string> {
    // In production, use proper hashing like bcrypt
    // For demo purposes, using simple base64
    return btoa(apiKey + '_salt_' + Date.now())
  }

  private createAPIKeyPreview(apiKey: string): string {
    // Show first 4 and last 4 characters for admin display
    if (apiKey.length < 8) return apiKey
    return `${apiKey.substring(0, 4)}...${apiKey.substring(apiKey.length - 4)}`
  }

  private mapAccessLogEntry(row: any): AccessLogEntry {
    return {
      serviceId: row.service_id,
      tokenId: row.token_id,
      endpoint: row.endpoint,
      method: row.method,
      statusCode: row.status_code,
      responseTimeMs: row.response_time_ms,
      requestSize: row.request_size,
      responseSize: row.response_size,
      ipAddress: row.ip_address,
      userAgent: row.user_agent,
      errorMessage: row.error_message,
      contextId: row.context_id,
      filePaths: row.file_paths ? JSON.parse(row.file_paths) : undefined,
      timestamp: row.timestamp
    }
  }

  private async executeQuery(query: string, params: any[]): Promise<any[]> {
    // This would be implemented with your database driver
    // For now, returning empty array as placeholder
    return []
  }

  private async initializeDatabase(): Promise<void> {
    // Initialize database connection and create tables if needed
    this.logger.info('Database initialized', 'initializeDatabase')
  }
}

// Export singleton instance
export const serviceAccessLogger = new ServiceAccessLogger()
```

## Service Access Table Format

### Current Active Services

| Service ID | Service Name | Version | Developer | Status | Capabilities | Last Access | Total Requests | Success Rate | Data Transfer |
|------------|--------------|---------|-----------|--------|--------------|-------------|----------------|--------------|---------------|
| srv_1234567890 | FileProcessor Pro | 1.2.0 | Acme Corp | 🟢 Active | upload, read, process | 2024-01-15 11:45 | 1,247 | 98.2% | 2.3 GB ↑ / 1.1 GB ↓ |
| srv_2345678901 | ImageOptimizer | 2.1.0 | ImageTech | 🟢 Active | upload, process, thumbnail | 2024-01-15 11:30 | 856 | 99.1% | 5.7 GB ↑ / 3.2 GB ↓ |
| srv_3456789012 | DocumentParser | 1.0.5 | DocSoft | 🟡 Limited | read, process | 2024-01-15 10:15 | 423 | 95.8% | 890 MB ↑ / 1.2 GB ↓ |
| srv_4567890123 | BackupService | 3.0.0 | CloudSync | 🟢 Active | upload, read, delete | 2024-01-15 09:22 | 2,103 | 97.5% | 12.4 GB ↑ / 8.9 GB ↓ |
| srv_5678901234 | TestPlugin | 0.1.0 | DevStudio | 🔴 Suspended | upload | 2024-01-14 16:30 | 45 | 73.3% | 125 MB ↑ / 89 MB ↓ |

### Service Access Monitoring Dashboard

```typescript
// src/components/ServiceAccessDashboard.tsx
import React, { useState, useEffect } from 'react'
import { serviceAccessLogger } from '../services/serviceAccessLogger'

interface ServiceStats {
  id: string
  name: string
  version: string
  status: 'active' | 'suspended' | 'revoked'
  lastAccess: Date
  totalRequests: number
  successfulRequests: number
  failedRequests: number
  avgResponseTime: number
  totalBytesUploaded: number
  totalBytesDownloaded: number
  activeTokens: number
}

export const ServiceAccessDashboard: React.FC = () => {
  const [services, setServices] = useState<ServiceStats[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedService, setSelectedService] = useState<string | null>(null)

  useEffect(() => {
    loadServiceStats()
  }, [])

  const loadServiceStats = async () => {
    try {
      const stats = await serviceAccessLogger.getServiceAccessReport()
      setServices(stats)
    } catch (error) {
      console.error('Failed to load service stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return '🟢'
      case 'suspended': return '🟡'
      case 'revoked': return '🔴'
      default: return '⚪'
    }
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatSuccessRate = (successful: number, total: number) => {
    if (total === 0) return '0%'
    return ((successful / total) * 100).toFixed(1) + '%'
  }

  const revokeService = async (serviceId: string) => {
    if (confirm('Are you sure you want to revoke access for this service?')) {
      try {
        await serviceAccessLogger.revokeService(serviceId, 'Manual revocation by admin')
        await loadServiceStats()
      } catch (error) {
        console.error('Failed to revoke service:', error)
      }
    }
  }

  if (loading) {
    return <div className="loading">Loading service statistics...</div>
  }

  return (
    <div className="service-access-dashboard">
      <div className="dashboard-header">
        <h2>Service Access Dashboard</h2>
        <div className="stats-summary">
          <div className="stat-card">
            <h3>Total Services</h3>
            <p>{services.length}</p>
          </div>
          <div className="stat-card">
            <h3>Active Services</h3>
            <p>{services.filter(s => s.status === 'active').length}</p>
          </div>
          <div className="stat-card">
            <h3>Total Requests (30d)</h3>
            <p>{services.reduce((sum, s) => sum + s.totalRequests, 0).toLocaleString()}</p>
          </div>
          <div className="stat-card">
            <h3>Data Transfer (30d)</h3>
            <p>
              {formatBytes(services.reduce((sum, s) => sum + s.totalBytesUploaded, 0))} ↑<br/>
              {formatBytes(services.reduce((sum, s) => sum + s.totalBytesDownloaded, 0))} ↓
            </p>
          </div>
        </div>
      </div>

      <div className="services-table">
        <table>
          <thead>
            <tr>
              <th>Service</th>
              <th>Version</th>
              <th>Status</th>
              <th>Last Access</th>
              <th>Requests (30d)</th>
              <th>Success Rate</th>
              <th>Avg Response</th>
              <th>Data Transfer</th>
              <th>Active Tokens</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {services.map(service => (
              <tr key={service.id} className={`status-${service.status}`}>
                <td>
                  <div className="service-info">
                    <strong>{service.name}</strong>
                    <small>{service.id}</small>
                  </div>
                </td>
                <td>{service.version}</td>
                <td>
                  <span className="status-badge">
                    {getStatusIcon(service.status)} {service.status}
                  </span>
                </td>
                <td>
                  {service.lastAccess
                    ? new Date(service.lastAccess).toLocaleString()
                    : 'Never'
                  }
                </td>
                <td>{service.totalRequests.toLocaleString()}</td>
                <td>
                  <span className={`success-rate ${
                    formatSuccessRate(service.successfulRequests, service.totalRequests).startsWith('9')
                      ? 'high' : 'low'
                  }`}>
                    {formatSuccessRate(service.successfulRequests, service.totalRequests)}
                  </span>
                </td>
                <td>{service.avgResponseTime?.toFixed(0) || 0}ms</td>
                <td>
                  <div className="data-transfer">
                    <div>↑ {formatBytes(service.totalBytesUploaded)}</div>
                    <div>↓ {formatBytes(service.totalBytesDownloaded)}</div>
                  </div>
                </td>
                <td>
                  <span className="token-count">{service.activeTokens}</span>
                </td>
                <td>
                  <div className="actions">
                    <button
                      onClick={() => setSelectedService(service.id)}
                      className="btn-details"
                    >
                      Details
                    </button>
                    {service.status === 'active' && (
                      <button
                        onClick={() => revokeService(service.id)}
                        className="btn-revoke"
                      >
                        Revoke
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {selectedService && (
        <ServiceDetailsModal
          serviceId={selectedService}
          onClose={() => setSelectedService(null)}
        />
      )}
    </div>
  )
}

const ServiceDetailsModal: React.FC<{
  serviceId: string
  onClose: () => void
}> = ({ serviceId, onClose }) => {
  const [accessLogs, setAccessLogs] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadAccessLogs()
  }, [serviceId])

  const loadAccessLogs = async () => {
    try {
      const logs = await serviceAccessLogger.getAccessLogs(serviceId, undefined, undefined, 100)
      setAccessLogs(logs)
    } catch (error) {
      console.error('Failed to load access logs:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={e => e.stopPropagation()}>
        <div className="modal-header">
          <h3>Service Access Details</h3>
          <button onClick={onClose} className="close-btn">×</button>
        </div>

        <div className="modal-body">
          {loading ? (
            <div>Loading access logs...</div>
          ) : (
            <div className="access-logs">
              <h4>Recent Access Logs (Last 100)</h4>
              <table>
                <thead>
                  <tr>
                    <th>Timestamp</th>
                    <th>Endpoint</th>
                    <th>Method</th>
                    <th>Status</th>
                    <th>Response Time</th>
                    <th>Size</th>
                  </tr>
                </thead>
                <tbody>
                  {accessLogs.map((log: any, index) => (
                    <tr key={index} className={log.statusCode >= 400 ? 'error' : 'success'}>
                      <td>{new Date(log.timestamp).toLocaleString()}</td>
                      <td>{log.endpoint}</td>
                      <td>{log.method}</td>
                      <td>
                        <span className={`status-code ${log.statusCode >= 400 ? 'error' : 'success'}`}>
                          {log.statusCode}
                        </span>
                      </td>
                      <td>{log.responseTimeMs}ms</td>
                      <td>
                        {log.requestSize > 0 && `↑${formatBytes(log.requestSize)} `}
                        {log.responseSize > 0 && `↓${formatBytes(log.responseSize)}`}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
```

### Service Access Report (JSON Format)

```json
{
  "reportGenerated": "2024-01-15T12:00:00Z",
  "reportPeriod": "30 days",
  "summary": {
    "totalServices": 5,
    "activeServices": 3,
    "suspendedServices": 1,
    "revokedServices": 1,
    "totalRequests": 4674,
    "totalDataTransfer": {
      "uploaded": "21.4 GB",
      "downloaded": "14.5 GB"
    },
    "averageSuccessRate": "96.8%"
  },
  "services": [
    {
      "id": "srv_1234567890",
      "name": "FileProcessor Pro",
      "version": "1.2.0",
      "developer": {
        "name": "Acme Corp",
        "email": "<EMAIL>",
        "organization": "Acme Corporation"
      },
      "status": "active",
      "registeredAt": "2024-01-01T10:00:00Z",
      "lastAccess": "2024-01-15T11:45:00Z",
      "capabilities": ["file.upload", "file.read", "file.process"],
      "statistics": {
        "totalRequests": 1247,
        "successfulRequests": 1225,
        "failedRequests": 22,
        "successRate": "98.2%",
        "avgResponseTime": "245ms",
        "dataTransfer": {
          "uploaded": "2.3 GB",
          "downloaded": "1.1 GB"
        },
        "activeTokens": 3,
        "uniqueContexts": 15
      },
      "recentErrors": [
        {
          "timestamp": "2024-01-15T10:30:00Z",
          "endpoint": "/api/v1/files/upload",
          "statusCode": 413,
          "error": "File too large"
        }
      ]
    }
  ]
}
```

## Error Codes and Troubleshooting

### Common Error Codes

| Code | Message | Description | Solution |
|------|---------|-------------|----------|
| 401 | Unauthorized | Invalid or expired token | Refresh token or re-authenticate |
| 403 | Forbidden | Insufficient permissions | Check service capabilities |
| 413 | Payload Too Large | File exceeds size limit | Reduce file size or request limit increase |
| 429 | Too Many Requests | Rate limit exceeded | Implement exponential backoff |
| 500 | Internal Server Error | Server-side error | Check logs and retry |

### Rate Limiting

```
Rate Limits (per service):
- Authentication: 10 requests/minute
- File Upload: 100 requests/hour
- File Read: 1000 requests/hour
- File List: 500 requests/hour
- File Delete: 50 requests/hour

Headers returned:
- X-RateLimit-Limit: Maximum requests allowed
- X-RateLimit-Remaining: Requests remaining in window
- X-RateLimit-Reset: Time when limit resets (Unix timestamp)
```

### Security Considerations

1. **Token Security**
   - Access tokens expire in 1 hour
   - Refresh tokens expire in 30 days
   - All tokens are hashed in database
   - Failed authentication attempts are logged

2. **File Validation**
   - MIME type validation
   - File size limits enforced
   - Malicious file scanning (optional)
   - Path traversal protection

3. **Audit Trail**
   - All API calls logged with timestamps
   - IP addresses and user agents recorded
   - File paths and operations tracked
   - Failed attempts monitored

This comprehensive API documentation provides everything needed to integrate with the FileRoutingService, including authentication, all endpoints, code examples, and monitoring capabilities.
```
```
