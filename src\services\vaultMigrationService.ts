/**
 * Vault Migration Service
 * Handles vault creation, restoration, and migration with new context management system
 */

import { BaseService, ServiceError, ServiceErrorCode } from './base'
import { contextManagementService } from './contextManagementService'
import { contextVaultService } from './contextVaultService'
import { vaultUIManager } from './vaultUIManager'
import { ContextVault, ContextFolder, VaultRegistry } from '../types'

export interface VaultMigrationOptions {
  vaultRootPath: string
  templateType: 'default' | 'simple'
  forceRebuild?: boolean
}

export interface VaultRestorationResult {
  success: boolean
  restored: boolean
  vaults: ContextVault[]
  contexts: ContextFolder[]
  error?: string
  warnings?: string[]
}

export interface VaultCreationResult {
  success: boolean
  vaults: ContextVault[]
  contexts: ContextFolder[]
  error?: string
}

export class VaultMigrationService extends BaseService {
  private readonly CHATLO_FOLDER = '.chatlo'
  private readonly REGISTRY_FILE = 'vault-registry.json'
  private readonly BACKUP_SUFFIX = '_backup'

  constructor() {
    super({
      name: 'VaultMigrationService',
      autoInitialize: true
    })
  }

  protected async doInitialize(): Promise<void> {
    await this.waitForElectronAPI()
    this.logger.info('VaultMigrationService initialized successfully', 'doInitialize')
  }

  /**
   * Main migration entry point - handles both restoration and creation
   */
  async migrateVault(options: VaultMigrationOptions): Promise<VaultRestorationResult> {
    return await this.executeOperationOrThrow(
      'migrateVault',
      async () => {
        this.logger.info('Starting vault migration', 'migrateVault', {
          vaultRootPath: options.vaultRootPath,
          templateType: options.templateType,
          forceRebuild: options.forceRebuild
        })

        // Step 1: Check if folder exists and create if needed
        await this.ensureVaultRootExists(options.vaultRootPath)

        // Step 2: Check for existing .chatlo folder
        const chatloPath = `${options.vaultRootPath}/${this.CHATLO_FOLDER}`
        const chatloExists = await this.pathExists(chatloPath)

        if (chatloExists && !options.forceRebuild) {
          // Attempt restoration
          this.logger.info('Found existing .chatlo folder, attempting restoration', 'migrateVault')
          return await this.attemptVaultRestoration(options.vaultRootPath)
        } else {
          // Create new vault or rebuild
          this.logger.info('Creating new vault structure', 'migrateVault')
          return await this.createNewVault(options)
        }
      },
      { vaultRootPath: options.vaultRootPath, templateType: options.templateType }
    )
  }

  /**
   * Attempt to restore vault from existing .chatlo folder
   */
  private async attemptVaultRestoration(vaultRootPath: string): Promise<VaultRestorationResult> {
    const warnings: string[] = []
    
    try {
      // Step 1: Try to load existing registry
      const registryPath = `${vaultRootPath}/${this.CHATLO_FOLDER}/${this.REGISTRY_FILE}`
      const registryExists = await this.pathExists(registryPath)

      if (!registryExists) {
        this.logger.warn('Registry file not found, will rebuild metadata', 'attemptVaultRestoration')
        return await this.rebuildVaultMetadata(vaultRootPath)
      }

      // Step 2: Load and validate registry
      const registryContent = await window.electronAPI.vault.readFile(registryPath)
      const registry: VaultRegistry = JSON.parse(registryContent)

      // Step 3: Validate vault structure
      const validationResult = await this.validateVaultStructure(vaultRootPath, registry)
      
      if (!validationResult.valid) {
        this.logger.warn('Vault structure validation failed, rebuilding', 'attemptVaultRestoration', {
          issues: validationResult.issues
        })
        warnings.push(...validationResult.issues)
        return await this.rebuildVaultMetadata(vaultRootPath, registry)
      }

      // Step 4: Update registry with new context management format
      const updatedRegistry = await this.upgradeRegistryFormat(registry)
      await this.saveVaultRegistry(vaultRootPath, updatedRegistry)

      // Step 5: Save vault root path to settings
      await this.saveVaultRootPath(vaultRootPath)

      // Step 6: Refresh services
      await contextVaultService.loadVaults()

      this.logger.info('Vault restoration completed successfully', 'attemptVaultRestoration', {
        vaultCount: updatedRegistry.vaults.length,
        contextCount: updatedRegistry.vaults.reduce((sum, v) => sum + v.contexts.length, 0)
      })

      return {
        success: true,
        restored: true,
        vaults: updatedRegistry.vaults,
        contexts: updatedRegistry.vaults.flatMap(v => v.contexts),
        warnings: warnings.length > 0 ? warnings : undefined
      }

    } catch (error: any) {
      this.logger.error('Vault restoration failed', 'attemptVaultRestoration', error)
      
      // Fallback to rebuilding metadata
      this.logger.info('Falling back to metadata rebuild', 'attemptVaultRestoration')
      return await this.rebuildVaultMetadata(vaultRootPath)
    }
  }

  /**
   * Rebuild vault metadata from existing folder structure
   */
  private async rebuildVaultMetadata(vaultRootPath: string, existingRegistry?: VaultRegistry): Promise<VaultRestorationResult> {
    const warnings: string[] = []
    
    try {
      this.logger.info('Rebuilding vault metadata from folder structure', 'rebuildVaultMetadata')

      // Step 1: Scan for vault folders
      const vaultFolders = await this.scanForVaultFolders(vaultRootPath)
      
      if (vaultFolders.length === 0) {
        // No vault folders found, create new vault
        this.logger.info('No vault folders found, creating new vault', 'rebuildVaultMetadata')
        const createResult = await this.createNewVault({
          vaultRootPath,
          templateType: 'default'
        })
        return {
          success: createResult.success,
          restored: false,
          vaults: createResult.vaults,
          contexts: createResult.contexts,
          error: createResult.error,
          warnings: ['No existing vaults found, created new vault structure']
        }
      }

      // Step 2: Rebuild vault metadata
      const rebuiltVaults: ContextVault[] = []
      
      for (const vaultFolder of vaultFolders) {
        try {
          const vault = await this.rebuildVaultFromFolder(vaultRootPath, vaultFolder, existingRegistry)
          rebuiltVaults.push(vault)
        } catch (error: any) {
          this.logger.warn(`Failed to rebuild vault: ${vaultFolder}`, 'rebuildVaultMetadata', error)
          warnings.push(`Failed to rebuild vault: ${vaultFolder} - ${error.message}`)
        }
      }

      // Step 3: Create new registry
      const newRegistry: VaultRegistry = {
        version: '1.0.0',
        vaultRoot: vaultRootPath,
        vaults: rebuiltVaults,
        lastScan: new Date().toISOString(),
        preferences: {
          defaultVault: rebuiltVaults.length > 0 ? rebuiltVaults[0].id : null,
          defaultContext: rebuiltVaults.length > 0 && rebuiltVaults[0].contexts.length > 0 
            ? rebuiltVaults[0].contexts[0].id : null,
          autoOrganize: true,
          showEmptyHints: true
        }
      }

      // Step 4: Save registry and update services
      await this.saveVaultRegistry(vaultRootPath, newRegistry)
      await this.saveVaultRootPath(vaultRootPath)
      await contextVaultService.loadVaults()

      this.logger.info('Vault metadata rebuild completed', 'rebuildVaultMetadata', {
        vaultCount: rebuiltVaults.length,
        contextCount: rebuiltVaults.reduce((sum, v) => sum + v.contexts.length, 0),
        warnings: warnings.length
      })

      return {
        success: true,
        restored: true,
        vaults: rebuiltVaults,
        contexts: rebuiltVaults.flatMap(v => v.contexts),
        warnings: warnings.length > 0 ? warnings : undefined
      }

    } catch (error: any) {
      this.logger.error('Vault metadata rebuild failed', 'rebuildVaultMetadata', error)
      return {
        success: false,
        restored: false,
        vaults: [],
        contexts: [],
        error: `Failed to rebuild vault metadata: ${error.message}`
      }
    }
  }

  /**
   * Create completely new vault structure
   */
  private async createNewVault(options: VaultMigrationOptions): Promise<VaultCreationResult> {
    try {
      this.logger.info('Creating new vault structure', 'createNewVault', {
        templateType: options.templateType
      })

      // Use the new context management service
      const setupResult = await contextManagementService.setupVaultStructure({
        setupType: options.templateType,
        vaultRoot: options.vaultRootPath
      })

      if (!setupResult.success) {
        throw new Error(setupResult.error || 'Failed to create vault structure')
      }

      // Save vault root path to settings
      await this.saveVaultRootPath(options.vaultRootPath)

      // Refresh services
      await contextVaultService.loadVaults()

      this.logger.info('New vault created successfully', 'createNewVault', {
        vaultCount: setupResult.vaults.length,
        contextCount: setupResult.vaults.reduce((sum, v) => sum + v.contexts.length, 0)
      })

      return {
        success: true,
        vaults: setupResult.vaults,
        contexts: setupResult.vaults.flatMap(v => v.contexts)
      }

    } catch (error: any) {
      this.logger.error('New vault creation failed', 'createNewVault', error)
      return {
        success: false,
        vaults: [],
        contexts: [],
        error: `Failed to create new vault: ${error.message}`
      }
    }
  }

  /**
   * Ensure vault root directory exists
   */
  private async ensureVaultRootExists(vaultRootPath: string): Promise<void> {
    const exists = await this.pathExists(vaultRootPath)
    if (!exists) {
      const createResult = await window.electronAPI.vault.createDirectory(vaultRootPath)
      if (!createResult.success) {
        throw new ServiceError(
          ServiceErrorCode.FILE_SYSTEM_ERROR,
          `Failed to create vault root directory: ${createResult.error}`,
          { serviceName: this.serviceName, operation: 'ensureVaultRootExists' }
        )
      }
    }
  }

  /**
   * Check if path exists
   */
  private async pathExists(path: string): Promise<boolean> {
    try {
      const result = await window.electronAPI.vault.pathExists(path)
      return result.exists
    } catch (error) {
      return false
    }
  }

  /**
   * Validate vault structure integrity
   */
  private async validateVaultStructure(vaultRootPath: string, registry: VaultRegistry): Promise<{ valid: boolean; issues: string[] }> {
    const issues: string[] = []

    try {
      // Check if all vaults exist
      for (const vault of registry.vaults) {
        const vaultExists = await this.pathExists(vault.path)
        if (!vaultExists) {
          issues.push(`Vault folder missing: ${vault.path}`)
          continue
        }

        // Check if all contexts exist
        for (const context of vault.contexts) {
          const contextExists = await this.pathExists(context.path)
          if (!contextExists) {
            issues.push(`Context folder missing: ${context.path}`)
          }
        }
      }

      // Check shared dropbox
      const sharedDropboxPath = `${vaultRootPath}/shared-dropbox`
      const sharedExists = await this.pathExists(sharedDropboxPath)
      if (!sharedExists) {
        issues.push('Shared dropbox folder missing')
      }

      return {
        valid: issues.length === 0,
        issues
      }

    } catch (error: any) {
      issues.push(`Validation error: ${error.message}`)
      return { valid: false, issues }
    }
  }

  /**
   * Upgrade registry format to new context management system
   */
  private async upgradeRegistryFormat(registry: VaultRegistry): Promise<VaultRegistry> {
    // Update version
    const upgradedRegistry: VaultRegistry = {
      ...registry,
      version: '1.0.0',
      lastScan: new Date().toISOString()
    }

    // Upgrade vault and context formats if needed
    for (const vault of upgradedRegistry.vaults) {
      for (const context of vault.contexts) {
        // Ensure context has new ID format if it's using old format
        if (!context.id.startsWith('ctx_')) {
          const newId = this.generateNewContextId(context.name)
          this.logger.info(`Upgrading context ID: ${context.id} -> ${newId}`, 'upgradeRegistryFormat')
          context.id = newId
        }

        // Ensure context has all required metadata
        if (!context.aiInsights) {
          context.aiInsights = {
            suggestedActions: ['Add files', 'Start conversation', 'Organize content'],
            contextType: 'default',
            readinessScore: 0.1
          }
        }

        // Ensure context has stats
        if (!context.stats) {
          context.stats = {
            fileCount: 0,
            conversationCount: 0,
            lastModified: new Date().toISOString(),
            sizeBytes: 0
          }
        }
      }
    }

    return upgradedRegistry
  }

  /**
   * Scan for existing vault folders
   */
  private async scanForVaultFolders(vaultRootPath: string): Promise<string[]> {
    try {
      const entries = await window.electronAPI.vault.readDirectory(vaultRootPath)
      const vaultFolders: string[] = []

      for (const entry of entries) {
        if (entry.isDirectory &&
            !entry.name.startsWith('.') &&
            entry.name !== 'shared-dropbox') {

          // Check if it looks like a vault folder (contains contexts or has .vault metadata)
          const vaultPath = `${vaultRootPath}/${entry.name}`
          const hasVaultMetadata = await this.pathExists(`${vaultPath}/.vault`)
          const hasContexts = await this.hasContextFolders(vaultPath)

          if (hasVaultMetadata || hasContexts) {
            vaultFolders.push(entry.name)
          }
        }
      }

      return vaultFolders

    } catch (error: any) {
      this.logger.warn('Failed to scan for vault folders', 'scanForVaultFolders', error)
      return []
    }
  }

  /**
   * Check if folder has context subfolders
   */
  private async hasContextFolders(vaultPath: string): Promise<boolean> {
    try {
      const entries = await window.electronAPI.vault.readDirectory(vaultPath)

      for (const entry of entries) {
        if (entry.isDirectory) {
          // Check if it looks like a context (has .context folder or master.md)
          const contextPath = `${vaultPath}/${entry.name}`
          const hasContextMetadata = await this.pathExists(`${contextPath}/.context`)
          const hasMasterDoc = await this.pathExists(`${contextPath}/master.md`)

          if (hasContextMetadata || hasMasterDoc) {
            return true
          }
        }
      }

      return false

    } catch (error) {
      return false
    }
  }

  /**
   * Rebuild vault from existing folder structure
   */
  private async rebuildVaultFromFolder(vaultRootPath: string, vaultFolderName: string, existingRegistry?: VaultRegistry): Promise<ContextVault> {
    const vaultPath = `${vaultRootPath}/${vaultFolderName}`

    // Try to find existing vault metadata
    let vaultMetadata: any = null
    const metadataPath = `${vaultPath}/.vault/metadata.json`

    try {
      if (await this.pathExists(metadataPath)) {
        const metadataContent = await window.electronAPI.vault.readFile(metadataPath)
        vaultMetadata = JSON.parse(metadataContent)
      }
    } catch (error) {
      this.logger.warn(`Failed to read vault metadata: ${metadataPath}`, 'rebuildVaultFromFolder', error)
    }

    // Generate vault info
    const vaultId = vaultMetadata?.id || this.generateVaultId(vaultFolderName)
    const vaultName = vaultMetadata?.name || this.formatVaultName(vaultFolderName)
    const vaultColor = vaultMetadata?.color || this.getDefaultVaultColor(vaultFolderName)
    const vaultIcon = vaultMetadata?.icon || this.getDefaultVaultIcon(vaultFolderName)

    // Scan for contexts
    const contexts = await this.scanContextsInVault(vaultPath)

    const vault: ContextVault = {
      id: vaultId,
      name: vaultName,
      path: vaultPath,
      color: vaultColor,
      icon: vaultIcon,
      created: vaultMetadata?.created || new Date().toISOString(),
      lastAccessed: new Date().toISOString(),
      contexts
    }

    return vault
  }

  /**
   * Scan contexts within a vault folder
   */
  private async scanContextsInVault(vaultPath: string): Promise<ContextFolder[]> {
    const contexts: ContextFolder[] = []

    try {
      const entries = await window.electronAPI.vault.readDirectory(vaultPath)

      for (const entry of entries) {
        if (entry.isDirectory && !entry.name.startsWith('.')) {
          try {
            const context = await this.rebuildContextFromFolder(vaultPath, entry.name)
            contexts.push(context)
          } catch (error: any) {
            this.logger.warn(`Failed to rebuild context: ${entry.name}`, 'scanContextsInVault', error)
          }
        }
      }

    } catch (error: any) {
      this.logger.warn(`Failed to scan contexts in vault: ${vaultPath}`, 'scanContextsInVault', error)
    }

    return contexts
  }

  /**
   * Rebuild context from existing folder
   */
  private async rebuildContextFromFolder(vaultPath: string, contextFolderName: string): Promise<ContextFolder> {
    const contextPath = `${vaultPath}/${contextFolderName}`

    // Try to read existing metadata
    let contextMetadata: any = null
    const metadataPath = `${contextPath}/.context/metadata.json`

    try {
      if (await this.pathExists(metadataPath)) {
        const metadataContent = await window.electronAPI.vault.readFile(metadataPath)
        contextMetadata = JSON.parse(metadataContent)
      }
    } catch (error) {
      this.logger.warn(`Failed to read context metadata: ${metadataPath}`, 'rebuildContextFromFolder', error)
    }

    // Generate context info
    const contextId = contextMetadata?.id || this.generateNewContextId(contextFolderName)
    const contextName = contextMetadata?.name || this.formatContextName(contextFolderName)
    const description = contextMetadata?.description || `Restored context: ${contextName}`

    // Check for master document
    const masterDocPath = `${contextPath}/master.md`
    const hasMasterDoc = await this.pathExists(masterDocPath)
    let masterDocPreview = ''

    if (hasMasterDoc) {
      try {
        const masterContent = await window.electronAPI.vault.readFile(masterDocPath)
        masterDocPreview = masterContent.substring(0, 200) + (masterContent.length > 200 ? '...' : '')
      } catch (error) {
        this.logger.warn(`Failed to read master document: ${masterDocPath}`, 'rebuildContextFromFolder', error)
      }
    }

    const context: ContextFolder = {
      id: contextId,
      name: contextName,
      path: contextPath,
      description,
      color: contextMetadata?.color || '#9CA3AF',
      icon: contextMetadata?.icon || 'fa-folder',
      status: 'active',
      stats: {
        fileCount: 0, // Will be updated by file scan
        conversationCount: 0,
        lastModified: new Date().toISOString(),
        sizeBytes: 0
      },
      masterDoc: {
        exists: hasMasterDoc,
        path: hasMasterDoc ? 'master.md' : '',
        preview: masterDocPreview,
        wordCount: masterDocPreview.split(/\s+/).length,
        lastUpdated: new Date().toISOString()
      },
      aiInsights: {
        suggestedActions: ['Review restored content', 'Update documentation', 'Organize files'],
        contextType: contextMetadata?.contextType || 'restored',
        readinessScore: 0.5
      }
    }

    return context
  }

  /**
   * Save vault registry
   */
  private async saveVaultRegistry(vaultRootPath: string, registry: VaultRegistry): Promise<void> {
    const chatloPath = `${vaultRootPath}/${this.CHATLO_FOLDER}`
    const registryPath = `${chatloPath}/${this.REGISTRY_FILE}`

    // Ensure .chatlo directory exists
    await window.electronAPI.vault.createDirectory(chatloPath)

    // Save registry
    await window.electronAPI.vault.writeFile(registryPath, JSON.stringify(registry, null, 2))
  }

  /**
   * Save vault root path to settings
   */
  private async saveVaultRootPath(vaultRootPath: string): Promise<void> {
    if (window.electronAPI?.settings) {
      await window.electronAPI.settings.set('vault-root-path', vaultRootPath)
    }
  }

  /**
   * Generate new context ID with new format
   */
  private generateNewContextId(name: string): string {
    const cleanName = name.toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 30)

    const timestamp = Date.now().toString(36)
    const random = Math.random().toString(36).substring(2, 6)

    return `ctx_${cleanName}_${timestamp}_${random}`
  }

  /**
   * Generate vault ID
   */
  private generateVaultId(name: string): string {
    const cleanName = name.toLowerCase().replace(/[^a-z0-9]/g, '')
    const timestamp = Date.now().toString(36)
    return `vault_${cleanName}_${timestamp}`
  }

  /**
   * Format vault name from folder name
   */
  private formatVaultName(folderName: string): string {
    return folderName
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }

  /**
   * Format context name from folder name
   */
  private formatContextName(folderName: string): string {
    return folderName
      .replace(/[-_]/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }

  /**
   * Get default vault color based on name
   */
  private getDefaultVaultColor(folderName: string): string {
    if (folderName.includes('personal')) return '#8AB0BB'
    if (folderName.includes('work')) return '#FF8383'
    if (folderName.includes('research')) return '#10B981'
    return '#9CA3AF'
  }

  /**
   * Get default vault icon based on name
   */
  private getDefaultVaultIcon(folderName: string): string {
    if (folderName.includes('personal')) return 'fa-user'
    if (folderName.includes('work')) return 'fa-briefcase'
    if (folderName.includes('research')) return 'fa-microscope'
    return 'fa-folder'
  }

  /**
   * Wait for electronAPI to be available
   */
  private async waitForElectronAPI(): Promise<void> {
    let attempts = 0
    const maxAttempts = 50

    while (!window.electronAPI?.vault && attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 100))
      attempts++
    }

    if (!window.electronAPI?.vault) {
      throw new ServiceError(
        ServiceErrorCode.INITIALIZATION_ERROR,
        'ElectronAPI not available after waiting',
        { serviceName: this.serviceName, operation: 'waitForElectronAPI' }
      )
    }
  }
}

// Export singleton instance
export const vaultMigrationService = new VaultMigrationService()
