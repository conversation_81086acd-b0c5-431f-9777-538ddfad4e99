/**
 * ContextManagementService
 * The second heart of the system - handles context creation, management, and lifecycle
 * Implements portable vault design with carry-on capability
 */

import { BaseService, ServiceError, ServiceErrorCode } from './base'
import { contextVaultService } from './contextVaultService'
import { fileRoutingService } from './fileRoutingService'
import { ContextVault, ContextFolder, VaultRegistry } from '../types'

export interface ContextCreationOptions {
  name: string
  description?: string
  vaultType: 'Personal' | 'Work' | 'Custom'
  vaultPath?: string // For custom vault location
  template?: 'default' | 'project' | 'research' | 'personal' | 'minimal'
  color?: string
  icon?: string
  metadata?: Record<string, any>
}

export interface ContextMigrationOptions {
  sourceContextId: string
  targetVaultPath: string
  preserveHistory: boolean
  createBackup: boolean
}

export interface VaultSetupOptions {
  setupType: 'default' | 'simple'
  vaultRoot: string
  customVaults?: Array<{
    name: string
    path: string
    color: string
    icon: string
  }>
}

export interface ContextMetadata {
  id: string
  name: string
  description: string
  created: string
  lastModified: string
  version: string
  contextType: string
  color: string
  icon: string
  tags: string[]
  settings: {
    autoOrganize: boolean
    fileRouting: {
      documents: string
      images: string
      artifacts: string
    }
    aiInsights: boolean
    syncEnabled: boolean
  }
  statistics: {
    fileCount: number
    totalSize: number
    conversationCount: number
    lastActivity: string
  }
  portability: {
    isPortable: boolean
    exportVersion: string
    dependencies: string[]
    attachedAt: string
    detachedAt?: string
  }
}

export class ContextManagementService extends BaseService {
  private readonly CONTEXT_VERSION = '1.0.0'
  private readonly METADATA_FILE = '.context/metadata.json'
  private readonly MANIFEST_FILE = '.context/manifest.json'
  private readonly SETTINGS_FILE = '.context/settings.json'

  constructor() {
    super({
      name: 'ContextManagementService',
      autoInitialize: true
    })
  }

  protected async doInitialize(): Promise<void> {
    await this.waitForElectronAPI()
    this.logger.info('ContextManagementService initialized successfully', 'doInitialize')
  }

  /**
   * Create a new context with full metadata and folder structure
   */
  async createContext(options: ContextCreationOptions): Promise<{ success: boolean; context?: ContextFolder; error?: string }> {
    return await this.executeOperationOrThrow(
      'createContext',
      async () => {
        // Generate stable context ID
        const contextId = this.generateContextId(options.name)
        
        // Determine target vault
        const targetVault = await this.resolveTargetVault(options)
        if (!targetVault) {
          throw new ServiceError(
            ServiceErrorCode.VALIDATION_ERROR,
            `Target vault not found for type: ${options.vaultType}`,
            { serviceName: this.serviceName, operation: 'createContext' }
          )
        }

        // Create context path
        const contextPath = `${targetVault.path}/${contextId}`
        
        // Check if context already exists
        const existsResult = await window.electronAPI.vault.pathExists(contextPath)
        if (existsResult.exists) {
          throw new ServiceError(
            ServiceErrorCode.VALIDATION_ERROR,
            `Context with ID '${contextId}' already exists`,
            { serviceName: this.serviceName, operation: 'createContext' }
          )
        }

        // Create context directory structure
        await this.createContextStructure(contextPath, options.template || 'default')

        // Create context metadata
        const metadata = await this.createContextMetadata(contextId, options, contextPath)
        await this.saveContextMetadata(contextPath, metadata)

        // Create manifest for portability
        await this.createContextManifest(contextPath, metadata)

        // Create settings file
        await this.createContextSettings(contextPath, options)

        // Create master.md file
        await this.createMasterDocument(contextPath, options)

        // Register context in vault
        await this.registerContextInVault(targetVault, contextId, contextPath, metadata)

        // Create context folder object
        const context: ContextFolder = {
          id: contextId,
          name: options.name,
          path: contextPath,
          description: options.description || `Context vault for ${options.name}`,
          color: options.color || this.getDefaultColor(options.vaultType),
          icon: options.icon || this.getDefaultIcon(options.template || 'default'),
          status: 'empty',
          stats: {
            fileCount: 1, // master.md
            conversationCount: 0,
            lastModified: new Date().toISOString(),
            sizeBytes: 0
          },
          masterDoc: {
            exists: true,
            path: 'master.md',
            preview: `# ${options.name}\n\n${options.description || ''}`,
            wordCount: 0,
            lastUpdated: new Date().toISOString()
          },
          aiInsights: {
            suggestedActions: this.getInitialSuggestions(options.template || 'default'),
            contextType: options.template || 'default',
            readinessScore: 0.1
          }
        }

        this.logger.info(`Context created successfully: ${options.name}`, 'createContext', {
          contextId,
          contextPath,
          vaultType: options.vaultType
        })

        return context
      },
      { contextName: options.name, vaultType: options.vaultType }
    )
  }

  /**
   * Setup vault structure based on user preferences
   */
  async setupVaultStructure(options: VaultSetupOptions): Promise<{ success: boolean; vaults: ContextVault[]; error?: string }> {
    return await this.executeOperationOrThrow(
      'setupVaultStructure',
      async () => {
        const vaults: ContextVault[] = []

        // Create vault root
        await window.electronAPI.vault.createDirectory(options.vaultRoot)

        // Create system directory
        const systemDir = `${options.vaultRoot}/.chatlo`
        await window.electronAPI.vault.createDirectory(systemDir)

        // Create shared dropbox
        await this.createSharedDropbox(options.vaultRoot)

        if (options.setupType === 'default') {
          // Create Personal and Work vaults
          const personalVault = await this.createVault(options.vaultRoot, 'Personal Vault', 'personal-vault', '#8AB0BB', 'fa-user')
          const workVault = await this.createVault(options.vaultRoot, 'Work Vault', 'work-vault', '#FF8383', 'fa-briefcase')

          // Create default contexts
          const personalContext = await this.createDefaultContext(personalVault, 'Your First Personal Context', 'personal')
          const workContext = await this.createDefaultContext(workVault, 'Your First Project Context', 'project')

          personalVault.contexts = [personalContext]
          workVault.contexts = [workContext]

          vaults.push(personalVault, workVault)
        } else if (options.setupType === 'simple') {
          // Create single vault
          const simpleVault = await this.createVault(options.vaultRoot, 'Personal Vault', 'personal-vault', '#8AB0BB', 'fa-folder')
          const simpleContext = await this.createDefaultContext(simpleVault, 'Your First Personal Context', 'personal')
          
          simpleVault.contexts = [simpleContext]
          vaults.push(simpleVault)
        }

        // Add custom vaults if specified
        if (options.customVaults) {
          for (const customVault of options.customVaults) {
            const vault = await this.createVault(
              options.vaultRoot,
              customVault.name,
              customVault.path,
              customVault.color,
              customVault.icon
            )
            vaults.push(vault)
          }
        }

        // Create and save vault registry
        const registry: VaultRegistry = {
          version: this.CONTEXT_VERSION,
          vaultRoot: options.vaultRoot,
          vaults,
          lastScan: new Date().toISOString(),
          preferences: {
            defaultVault: vaults.length > 0 ? vaults[0].id : null,
            defaultContext: vaults.length > 0 && vaults[0].contexts.length > 0 ? vaults[0].contexts[0].id : null,
            autoOrganize: true,
            showEmptyHints: true
          }
        }

        await this.saveVaultRegistry(registry)

        this.logger.info(`Vault structure created: ${options.setupType}`, 'setupVaultStructure', {
          vaultRoot: options.vaultRoot,
          vaultCount: vaults.length
        })

        return vaults
      },
      { setupType: options.setupType, vaultRoot: options.vaultRoot }
    )
  }

  /**
   * Generate stable context ID from name
   */
  private generateContextId(name: string): string {
    // Create a stable ID based on name and timestamp
    const cleanName = name.toLowerCase()
      .replace(/[^a-z0-9\s]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .substring(0, 30) // Limit length
    
    const timestamp = Date.now().toString(36)
    const random = Math.random().toString(36).substring(2, 6)
    
    return `ctx_${cleanName}_${timestamp}_${random}`
  }

  /**
   * Create context directory structure
   */
  private async createContextStructure(contextPath: string, template: string): Promise<void> {
    this.logger.info(`Creating context structure at: ${contextPath}`, 'createContextStructure', { template })

    // Create main context directory
    await window.electronAPI.vault.createDirectory(contextPath)
    this.logger.info(`Created main context directory: ${contextPath}`, 'createContextStructure')

    // Create standard subdirectories
    const subdirs = ['documents', 'images', 'artifacts', '.context']
    for (const subdir of subdirs) {
      const subdirPath = `${contextPath}/${subdir}`
      await window.electronAPI.vault.createDirectory(subdirPath)
      this.logger.info(`Created subdirectory: ${subdirPath}`, 'createContextStructure')
    }

    // Create template-specific directories
    if (template === 'research') {
      await window.electronAPI.vault.createDirectory(`${contextPath}/references`)
      await window.electronAPI.vault.createDirectory(`${contextPath}/notes`)
      this.logger.info(`Created research-specific directories for: ${contextPath}`, 'createContextStructure')
    } else if (template === 'project') {
      await window.electronAPI.vault.createDirectory(`${contextPath}/planning`)
      await window.electronAPI.vault.createDirectory(`${contextPath}/resources`)
      this.logger.info(`Created project-specific directories for: ${contextPath}`, 'createContextStructure')
    }

    this.logger.info(`Context structure creation completed: ${contextPath}`, 'createContextStructure')
  }

  /**
   * Create context metadata
   */
  private async createContextMetadata(contextId: string, options: ContextCreationOptions, contextPath: string): Promise<ContextMetadata> {
    const now = new Date().toISOString()

    return {
      id: contextId,
      name: options.name,
      description: options.description || `Context vault for ${options.name}`,
      created: now,
      lastModified: now,
      version: this.CONTEXT_VERSION,
      contextType: options.template || 'default',
      color: options.color || this.getDefaultColor(options.vaultType),
      icon: options.icon || this.getDefaultIcon(options.template || 'default'),
      tags: [],
      settings: {
        autoOrganize: true,
        fileRouting: {
          documents: 'documents',
          images: 'images',
          artifacts: 'artifacts'
        },
        aiInsights: true,
        syncEnabled: true
      },
      statistics: {
        fileCount: 0,
        totalSize: 0,
        conversationCount: 0,
        lastActivity: now
      },
      portability: {
        isPortable: true,
        exportVersion: this.CONTEXT_VERSION,
        dependencies: [],
        attachedAt: now
      }
    }
  }

  /**
   * Save context metadata to file
   */
  private async saveContextMetadata(contextPath: string, metadata: ContextMetadata): Promise<void> {
    const metadataPath = `${contextPath}/${this.METADATA_FILE}`
    await window.electronAPI.vault.writeFile(metadataPath, JSON.stringify(metadata, null, 2))
  }

  /**
   * Create context manifest for portability
   */
  private async createContextManifest(contextPath: string, metadata: ContextMetadata): Promise<void> {
    const manifest = {
      contextId: metadata.id,
      name: metadata.name,
      version: metadata.version,
      created: metadata.created,
      structure: {
        folders: ['documents', 'images', 'artifacts', '.context'],
        requiredFiles: ['.context/metadata.json', '.context/settings.json', 'master.md'],
        optionalFiles: ['.context/manifest.json']
      },
      portability: {
        isPortable: true,
        canDetach: true,
        canReattach: true,
        preservesHistory: true
      },
      compatibility: {
        minVersion: '1.0.0',
        maxVersion: '2.0.0'
      }
    }

    const manifestPath = `${contextPath}/${this.MANIFEST_FILE}`
    await window.electronAPI.vault.writeFile(manifestPath, JSON.stringify(manifest, null, 2))
  }

  /**
   * Create context settings file
   */
  private async createContextSettings(contextPath: string, options: ContextCreationOptions): Promise<void> {
    const settings = {
      preferences: {
        theme: options.color || this.getDefaultColor(options.vaultType),
        icon: options.icon || this.getDefaultIcon(options.template || 'default'),
        autoSave: true,
        notifications: true
      },
      fileHandling: {
        autoRoute: true,
        duplicateHandling: 'rename',
        maxFileSize: 100 * 1024 * 1024, // 100MB
        allowedTypes: ['*']
      },
      ai: {
        enableInsights: true,
        autoSummarize: false,
        suggestActions: true
      },
      sync: {
        enabled: false,
        provider: null,
        lastSync: null
      }
    }

    const settingsPath = `${contextPath}/${this.SETTINGS_FILE}`
    await window.electronAPI.vault.writeFile(settingsPath, JSON.stringify(settings, null, 2))
  }

  /**
   * Create master document
   */
  private async createMasterDocument(contextPath: string, options: ContextCreationOptions): Promise<void> {
    const template = this.getMasterDocTemplate(options.template || 'default', options)
    const masterPath = `${contextPath}/master.md`
    await window.electronAPI.vault.writeFile(masterPath, template)
  }

  /**
   * Get master document template based on context type
   */
  private getMasterDocTemplate(template: string, options: ContextCreationOptions): string {
    const date = new Date().toLocaleDateString()
    const baseTemplate = `# ${options.name}

**Created:** ${date}
**Type:** ${template}
**Description:** ${options.description || 'Context vault for organizing files and conversations'}

## Overview

This is your context vault for ${options.name}. Use this space to organize your thoughts, files, and conversations.

## Quick Start

1. **Add Files**: Drag and drop files into the appropriate folders
   - 📄 Documents → \`/documents\`
   - 🖼️ Images → \`/images\`
   - 🔧 Artifacts → \`/artifacts\`

2. **Start Conversations**: Begin chatting with AI about your context
3. **Organize**: Use this master document to keep track of your progress

## Notes

<!-- Your notes and thoughts go here -->

---
*This context is portable and can be moved between different ChatLo installations while preserving all data and history.*
`

    if (template === 'project') {
      return baseTemplate + `

## Project Details

**Status:** Planning
**Priority:** Medium
**Deadline:** TBD

### Objectives
- [ ] Define project scope
- [ ] Gather requirements
- [ ] Create timeline
- [ ] Begin implementation

### Resources
- Planning documents in \`/planning\`
- Project resources in \`/resources\`
- Meeting notes and decisions

### Next Steps
1. Review project requirements
2. Set up project timeline
3. Identify key stakeholders
`
    } else if (template === 'research') {
      return baseTemplate + `

## Research Focus

**Topic:** ${options.name}
**Status:** Initial Research
**Last Updated:** ${date}

### Research Questions
1. What are the key aspects to investigate?
2. What sources should be consulted?
3. What methodology will be used?

### Sources & References
- Academic papers in \`/references\`
- Research notes in \`/notes\`
- Supporting documents in \`/documents\`

### Findings
<!-- Document your research findings here -->

### Next Steps
1. Define research scope
2. Gather initial sources
3. Begin systematic review
`
    }

    return baseTemplate
  }

  /**
   * Resolve target vault based on options
   */
  private async resolveTargetVault(options: ContextCreationOptions): Promise<ContextVault | null> {
    const vaults = contextVaultService.getCurrentVaults()

    if (options.vaultPath) {
      // Custom vault path specified
      return vaults.find(v => v.path === options.vaultPath) || null
    }

    // Find vault by type with improved matching
    if (options.vaultType === 'Personal') {
      return vaults.find(v => v.name.toLowerCase().includes('personal')) || null
    } else if (options.vaultType === 'Work') {
      return vaults.find(v => v.name.toLowerCase().includes('work')) || null
    } else if (options.vaultType === 'Custom') {
      // For custom type, return the first available vault if no specific path
      return vaults[0] || null
    }

    // Fallback: try to match by name
    return vaults.find(v =>
      v.name.toLowerCase().includes(options.vaultType.toLowerCase())
    ) || null
  }

  /**
   * Register context in vault
   */
  private async registerContextInVault(vault: ContextVault, contextId: string, contextPath: string, metadata: ContextMetadata): Promise<void> {
    // This would typically update the vault registry
    // For now, we'll rely on the existing vault scanning mechanism
    await contextVaultService.loadVaults()
  }

  /**
   * Create shared dropbox
   */
  private async createSharedDropbox(vaultRoot: string): Promise<void> {
    const sharedPath = `${vaultRoot}/shared-dropbox`
    await window.electronAPI.vault.createDirectory(sharedPath)

    const metadata = {
      name: 'Shared Dropbox',
      description: 'Raw data storage for unclassified files',
      created: new Date().toISOString(),
      type: 'shared-storage'
    }

    await window.electronAPI.vault.writeFile(
      `${sharedPath}/.metadata.json`,
      JSON.stringify(metadata, null, 2)
    )
  }

  /**
   * Create a vault
   */
  private async createVault(rootPath: string, name: string, folderName: string, color: string, icon: string): Promise<ContextVault> {
    const vaultPath = `${rootPath}/${folderName}`
    const vaultId = this.generateVaultId(name)

    await window.electronAPI.vault.createDirectory(vaultPath)

    const metadataDir = `${vaultPath}/.vault`
    await window.electronAPI.vault.createDirectory(metadataDir)

    const metadata = {
      id: vaultId,
      name,
      created: new Date().toISOString(),
      color,
      icon,
      description: `${name} for organizing your files and conversations`
    }

    await window.electronAPI.vault.writeFile(
      `${metadataDir}/metadata.json`,
      JSON.stringify(metadata, null, 2)
    )

    return {
      id: vaultId,
      name,
      path: vaultPath,
      color,
      icon,
      created: new Date().toISOString(),
      lastAccessed: new Date().toISOString(),
      contexts: []
    }
  }

  /**
   * Create default context for a vault
   */
  private async createDefaultContext(vault: ContextVault, name: string, template: string): Promise<ContextFolder> {
    this.logger.info(`Creating default context: ${name} for vault: ${vault.name}`, 'createDefaultContext', {
      vaultPath: vault.path,
      template
    })

    // Determine vault type from vault name
    let vaultType: 'Personal' | 'Work' | 'Custom' = 'Custom'
    if (vault.name.toLowerCase().includes('personal')) {
      vaultType = 'Personal'
    } else if (vault.name.toLowerCase().includes('work')) {
      vaultType = 'Work'
    }

    this.logger.info(`Resolved vault type: ${vaultType} for vault: ${vault.name}`, 'createDefaultContext')

    const options: ContextCreationOptions = {
      name,
      description: `Default ${template} context`,
      vaultType,
      vaultPath: vault.path,
      template,
      color: vault.color,
      icon: template === 'personal' ? 'fa-lightbulb' : 'fa-folder-open'
    }

    const result = await this.createContext(options)
    if (!result.success || !result.context) {
      throw new ServiceError(
        ServiceErrorCode.CREATION_ERROR,
        `Failed to create default context: ${result.error}`,
        { serviceName: this.serviceName, operation: 'createDefaultContext' }
      )
    }

    return result.context
  }

  /**
   * Save vault registry
   */
  private async saveVaultRegistry(registry: VaultRegistry): Promise<void> {
    await window.electronAPI.vault.saveVaultRegistry(registry)
  }

  /**
   * Generate vault ID
   */
  private generateVaultId(name: string): string {
    const cleanName = name.toLowerCase().replace(/[^a-z0-9]/g, '')
    const timestamp = Date.now().toString(36)
    return `vault_${cleanName}_${timestamp}`
  }

  /**
   * Get default color based on vault type
   */
  private getDefaultColor(vaultType: string): string {
    switch (vaultType.toLowerCase()) {
      case 'personal': return '#8AB0BB'
      case 'work': return '#FF8383'
      default: return '#9CA3AF'
    }
  }

  /**
   * Get default icon based on template
   */
  private getDefaultIcon(template: string): string {
    switch (template) {
      case 'project': return 'fa-folder-open'
      case 'research': return 'fa-search'
      case 'personal': return 'fa-lightbulb'
      case 'minimal': return 'fa-file'
      default: return 'fa-folder'
    }
  }

  /**
   * Get initial suggestions based on template
   */
  private getInitialSuggestions(template: string): string[] {
    switch (template) {
      case 'project':
        return ['Define project scope', 'Create timeline', 'Add team members', 'Set up resources']
      case 'research':
        return ['Define research questions', 'Gather sources', 'Create methodology', 'Begin analysis']
      case 'personal':
        return ['Add personal files', 'Start journaling', 'Organize thoughts', 'Set goals']
      default:
        return ['Add files', 'Start conversation', 'Organize content', 'Set up structure']
    }
  }

  /**
   * Wait for electronAPI to be available
   */
  private async waitForElectronAPI(): Promise<void> {
    let attempts = 0
    const maxAttempts = 50

    while (!window.electronAPI?.vault && attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, 100))
      attempts++
    }

    if (!window.electronAPI?.vault) {
      throw new ServiceError(
        ServiceErrorCode.INITIALIZATION_ERROR,
        'ElectronAPI not available after waiting',
        { serviceName: this.serviceName, operation: 'waitForElectronAPI' }
      )
    }
  }
}

// Export singleton instance
export const contextManagementService = new ContextManagementService()
