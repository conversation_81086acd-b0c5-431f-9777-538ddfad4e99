# ChatLo Context System Architecture

## System Overview

The ChatLo context system is built around two core services that work together to provide a comprehensive file and context management solution:

1. **File Routing Service** - The first heart: Handles all file operations with intelligent routing
2. **Context Management Service** - The second heart: Manages context creation, lifecycle, and portability

## Architectural Principles

### 1. Portable & Carry-On Design
- **Self-Contained Contexts**: Each context folder contains all necessary metadata and settings
- **USB Portability**: Contexts can be copied to USB drives and work on any ChatLo installation
- **Zero-Loss Migration**: Moving contexts preserves all history, settings, and file relationships
- **Version Compatibility**: Forward and backward compatibility through versioned metadata

### 2. Intelligent File Routing
- **Auto-Classification**: Files are automatically routed based on MIME type and content analysis
- **Context-Aware**: Routing respects individual context preferences and overrides
- **Extensible**: Plugin system allows custom routing rules and processors
- **Consistent**: Same routing logic across all drop zones and upload methods

### 3. Flexible Vault Structure
- **Adaptive Setup**: Supports both simple and complex organizational structures
- **User Choice**: Default (Personal + Work) or Simple (Single vault) configurations
- **Extensible**: Custom vaults can be added for specialized use cases
- **Scalable**: Structure grows with user needs without breaking existing workflows

## Core Components

### File Routing Service (`fileRoutingService.ts`)

**Responsibilities:**
- Unified file handling across all drop zones
- Intelligent auto-routing based on file type
- File validation and security checks
- Integration with context vault system
- Plugin system for extensible processing

**Key Features:**
- Replaces complex SharedDropboxService with unified approach
- Supports both context-specific and shared uploads
- Automatic folder routing (documents/images/artifacts)
- Comprehensive error handling and logging
- Plugin architecture for future extensions

### Context Management Service (`contextManagementService.ts`)

**Responsibilities:**
- Context creation with templates and customization
- Vault structure initialization and management
- Context metadata and portability features
- Template system for different use cases
- Context lifecycle management

**Key Features:**
- Smart context ID generation for stability and readability
- Multiple context templates (default, project, research, personal, minimal)
- Complete portability with manifest and metadata files
- Flexible vault setup (default vs simple)
- Integration with existing vault system

## Data Flow Architecture

### File Upload Flow
```
1. User drops files → FileRoutingService.handleFileDrop()
2. File validation → validateFiles()
3. Destination resolution → resolveDestinationPath()
4. Auto-routing logic → getAutoRouteFolder()
5. File processing → processFile()
6. Context integration → contextVaultService integration
7. Response & logging → Success/error response with metrics
```

### Context Creation Flow
```
1. User creates context → ContextManagementService.createContext()
2. ID generation → generateContextId()
3. Vault resolution → resolveTargetVault()
4. Structure creation → createContextStructure()
5. Metadata creation → createContextMetadata()
6. File generation → createMasterDocument(), createManifest()
7. Registration → registerContextInVault()
8. UI update → contextVaultService.loadVaults()
```

## Folder Structure Design

### Complete Vault Structure
```
File Vault Root/
├── .chatlo/                           # System metadata
│   ├── vault-registry.json           # Master vault registry
│   └── settings.json                 # Global settings
├── shared-dropbox/                    # Unclassified file storage
│   ├── .metadata.json               # Dropbox metadata
│   ├── .files.json                  # File tracking
│   └── [uploaded files]             # Raw user files
├── personal-vault/                    # Personal contexts
│   └── ctx_research_notes_abc123/    # Example personal context
│       ├── .context/                 # Context system files
│       │   ├── metadata.json        # Context metadata
│       │   ├── manifest.json        # Portability manifest
│       │   └── settings.json        # Context settings
│       ├── documents/                # Text files, PDFs
│       ├── images/                   # Image files
│       ├── artifacts/                # Generated content
│       ├── references/               # Research-specific folder
│       ├── notes/                    # Research-specific folder
│       └── master.md                 # Context master document
└── work-vault/                        # Work contexts
    └── ctx_website_project_def456/    # Example work context
        ├── .context/
        ├── documents/
        ├── images/
        ├── artifacts/
        ├── planning/                  # Project-specific folder
        ├── resources/                 # Project-specific folder
        └── master.md
```

### Context Metadata Structure
Each context maintains comprehensive metadata in `.context/metadata.json`:

```json
{
  "id": "ctx_research_notes_1704123456_abc1",
  "name": "AI Research Notes",
  "description": "Research on machine learning algorithms",
  "created": "2024-01-15T10:30:00Z",
  "lastModified": "2024-01-15T11:45:00Z",
  "version": "1.0.0",
  "contextType": "research",
  "color": "#4F46E5",
  "icon": "fa-brain",
  "tags": ["ai", "research", "machine-learning"],
  "settings": {
    "autoOrganize": true,
    "fileRouting": {
      "documents": "documents",
      "images": "images", 
      "artifacts": "artifacts"
    },
    "aiInsights": true,
    "syncEnabled": false
  },
  "statistics": {
    "fileCount": 15,
    "totalSize": ********,
    "conversationCount": 3,
    "lastActivity": "2024-01-15T11:45:00Z"
  },
  "portability": {
    "isPortable": true,
    "exportVersion": "1.0.0",
    "dependencies": [],
    "attachedAt": "2024-01-15T10:30:00Z"
  }
}
```

## Context ID Management

### Smart ID Generation Strategy
The system uses a multi-component ID strategy for optimal balance of readability and uniqueness:

**Format**: `ctx_{clean-name}_{timestamp}_{random}`

**Components**:
- `ctx_` - Fixed prefix for easy identification
- `{clean-name}` - Sanitized context name (max 30 chars)
- `{timestamp}` - Base36 timestamp for chronological ordering
- `{random}` - 4-character random suffix for collision prevention

**Examples**:
- `ctx_machine_learning_research_1k2m3n4p_a1b2`
- `ctx_website_redesign_1k2m3n5q_c3d4`
- `ctx_personal_notes_1k2m3n6r_e5f6`

**Benefits**:
- Human readable and meaningful
- Chronologically sortable
- Collision resistant
- Stable across operations
- Easy to identify in logs and debugging

## Integration Points

### File Routing ↔ Context Management
- Context creation registers file routing preferences
- File routing service respects context-specific settings
- Auto-routing rules can be customized per context
- File operations update context statistics

### Context Management ↔ Vault Service
- New contexts automatically registered in vault registry
- Context selection triggers UI updates
- Vault scanning validates context metadata
- Context operations logged in vault history

### Both Services ↔ UI Components
- Consistent error handling and user feedback
- Real-time updates through service subscriptions
- Unified logging and metrics collection
- Standardized configuration management

## Portability Implementation

### Context Export Process
1. **Validation**: Verify context integrity and completeness
2. **Manifest Creation**: Generate portability manifest with dependencies
3. **Metadata Packaging**: Include all context metadata and settings
4. **File Verification**: Ensure all referenced files are present
5. **Version Tagging**: Mark export version for compatibility checking

### Context Import Process
1. **Compatibility Check**: Verify version compatibility
2. **Structure Validation**: Ensure required folders and files exist
3. **Metadata Restoration**: Import context settings and preferences
4. **Integration**: Register with local vault system
5. **Verification**: Confirm successful import and functionality

### Carry-On Scenarios
- **USB Transfer**: Copy context folder to USB, plug into another machine
- **Cloud Sync**: Sync context folder through Dropbox/OneDrive
- **Network Share**: Access context from shared network location
- **Backup Restore**: Restore context from backup archive

## Error Handling Strategy

### Service-Level Error Handling
- Comprehensive error categorization (validation, file system, network)
- Graceful degradation when services are unavailable
- Automatic retry logic for transient failures
- Detailed error logging for debugging

### User-Facing Error Messages
- Clear, actionable error messages
- Suggested solutions for common problems
- Progress indicators for long-running operations
- Rollback capabilities for failed operations

## Performance Considerations

### File Operations
- Asynchronous file processing to prevent UI blocking
- Batch operations for multiple file uploads
- Progress tracking for large file operations
- Efficient file validation and type detection

### Context Operations
- Lazy loading of context metadata
- Cached vault registry for fast access
- Incremental context scanning
- Optimized file tree generation

## Security Features

### File Security
- MIME type validation and sanitization
- File size limits and quota management
- Path traversal protection
- Optional malware scanning integration

### Context Security
- Metadata validation and sanitization
- Access control for sensitive contexts
- Audit logging for all context operations
- Secure handling of user preferences

## Future Extensibility

### Plugin Architecture
- File processor plugins for custom file types
- Context template plugins for specialized workflows
- Routing rule plugins for custom organization
- Integration plugins for external services

### API Extensions
- RESTful API for external integrations
- Webhook support for real-time notifications
- Batch operation APIs for automation
- Analytics APIs for usage insights

This architecture provides a solid foundation for the ChatLo context system while maintaining flexibility for future enhancements and user customization.
