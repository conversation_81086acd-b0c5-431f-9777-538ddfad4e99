/**
 * Test suite for VaultMigrationService
 * Tests vault creation, restoration, and migration functionality
 */

import { vaultMigrationService } from '../services/vaultMigrationService'

// Mock electron API
const mockElectronAPI = {
  vault: {
    pathExists: jest.fn(),
    readFile: jest.fn(),
    writeFile: jest.fn(),
    createDirectory: jest.fn(),
    readDirectory: jest.fn()
  },
  settings: {
    get: jest.fn(),
    set: jest.fn()
  }
}

// Mock window.electronAPI
Object.defineProperty(window, 'electronAPI', {
  value: mockElectronAPI,
  writable: true
})

describe('VaultMigrationService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Service Initialization', () => {
    test('should initialize successfully', async () => {
      expect(vaultMigrationService).toBeDefined()
      expect(vaultMigrationService.serviceName).toBe('VaultMigrationService')
    })
  })

  describe('New Vault Creation', () => {
    test('should create new vault when no .chatlo folder exists', async () => {
      // Mock no existing .chatlo folder
      mockElectronAPI.vault.pathExists.mockResolvedValue({ exists: false })
      mockElectronAPI.vault.createDirectory.mockResolvedValue({ success: true })
      mockElectronAPI.vault.writeFile.mockResolvedValue({ success: true })
      mockElectronAPI.settings.set.mockResolvedValue(true)

      const result = await vaultMigrationService.migrateVault({
        vaultRootPath: '/test/vault',
        templateType: 'default'
      })

      expect(result.success).toBe(true)
      expect(result.restored).toBe(false)
      expect(mockElectronAPI.vault.createDirectory).toHaveBeenCalled()
    })

    test('should create simple vault template', async () => {
      mockElectronAPI.vault.pathExists.mockResolvedValue({ exists: false })
      mockElectronAPI.vault.createDirectory.mockResolvedValue({ success: true })
      mockElectronAPI.vault.writeFile.mockResolvedValue({ success: true })
      mockElectronAPI.settings.set.mockResolvedValue(true)

      const result = await vaultMigrationService.migrateVault({
        vaultRootPath: '/test/vault',
        templateType: 'simple'
      })

      expect(result.success).toBe(true)
      expect(result.restored).toBe(false)
    })
  })

  describe('Vault Restoration', () => {
    test('should restore vault when .chatlo folder exists with valid registry', async () => {
      // Mock existing .chatlo folder
      mockElectronAPI.vault.pathExists.mockImplementation((path: string) => {
        if (path.includes('.chatlo')) return Promise.resolve({ exists: true })
        if (path.includes('vault-registry.json')) return Promise.resolve({ exists: true })
        return Promise.resolve({ exists: true })
      })

      // Mock valid registry file
      const mockRegistry = {
        version: '1.0.0',
        vaultRoot: '/test/vault',
        vaults: [
          {
            id: 'vault_personal_123',
            name: 'Personal Vault',
            path: '/test/vault/personal-vault',
            color: '#8AB0BB',
            icon: 'fa-user',
            created: '2024-01-01T00:00:00.000Z',
            lastAccessed: '2024-01-01T00:00:00.000Z',
            contexts: []
          }
        ],
        lastScan: '2024-01-01T00:00:00.000Z',
        preferences: {
          defaultVault: 'vault_personal_123',
          defaultContext: null,
          autoOrganize: true,
          showEmptyHints: true
        }
      }

      mockElectronAPI.vault.readFile.mockResolvedValue(JSON.stringify(mockRegistry))
      mockElectronAPI.vault.writeFile.mockResolvedValue({ success: true })
      mockElectronAPI.settings.set.mockResolvedValue(true)

      const result = await vaultMigrationService.migrateVault({
        vaultRootPath: '/test/vault',
        templateType: 'default'
      })

      expect(result.success).toBe(true)
      expect(result.restored).toBe(true)
      expect(result.vaults).toHaveLength(1)
    })

    test('should rebuild metadata when registry is missing', async () => {
      // Mock existing .chatlo folder but no registry
      mockElectronAPI.vault.pathExists.mockImplementation((path: string) => {
        if (path.includes('.chatlo')) return Promise.resolve({ exists: true })
        if (path.includes('vault-registry.json')) return Promise.resolve({ exists: false })
        return Promise.resolve({ exists: true })
      })

      // Mock vault folders
      mockElectronAPI.vault.readDirectory.mockImplementation((path: string) => {
        if (path === '/test/vault') {
          return Promise.resolve([
            { name: 'personal-vault', isDirectory: true },
            { name: 'work-vault', isDirectory: true },
            { name: 'shared-dropbox', isDirectory: true }
          ])
        }
        return Promise.resolve([])
      })

      mockElectronAPI.vault.writeFile.mockResolvedValue({ success: true })
      mockElectronAPI.settings.set.mockResolvedValue(true)

      const result = await vaultMigrationService.migrateVault({
        vaultRootPath: '/test/vault',
        templateType: 'default'
      })

      expect(result.success).toBe(true)
      expect(result.restored).toBe(true)
      expect(result.warnings).toBeDefined()
    })
  })

  describe('Force Rebuild', () => {
    test('should force rebuild when forceRebuild is true', async () => {
      mockElectronAPI.vault.pathExists.mockResolvedValue({ exists: true })
      mockElectronAPI.vault.readDirectory.mockResolvedValue([])
      mockElectronAPI.vault.createDirectory.mockResolvedValue({ success: true })
      mockElectronAPI.vault.writeFile.mockResolvedValue({ success: true })
      mockElectronAPI.settings.set.mockResolvedValue(true)

      const result = await vaultMigrationService.migrateVault({
        vaultRootPath: '/test/vault',
        templateType: 'default',
        forceRebuild: true
      })

      expect(result.success).toBe(true)
      // Should create new vault instead of attempting restoration
    })
  })

  describe('Error Handling', () => {
    test('should handle file system errors gracefully', async () => {
      mockElectronAPI.vault.pathExists.mockRejectedValue(new Error('File system error'))

      const result = await vaultMigrationService.migrateVault({
        vaultRootPath: '/test/vault',
        templateType: 'default'
      })

      expect(result.success).toBe(false)
      expect(result.error).toContain('File system error')
    })

    test('should handle invalid registry files', async () => {
      mockElectronAPI.vault.pathExists.mockImplementation((path: string) => {
        if (path.includes('.chatlo')) return Promise.resolve({ exists: true })
        if (path.includes('vault-registry.json')) return Promise.resolve({ exists: true })
        return Promise.resolve({ exists: true })
      })

      // Mock invalid JSON
      mockElectronAPI.vault.readFile.mockResolvedValue('invalid json')
      mockElectronAPI.vault.readDirectory.mockResolvedValue([])
      mockElectronAPI.vault.writeFile.mockResolvedValue({ success: true })
      mockElectronAPI.settings.set.mockResolvedValue(true)

      const result = await vaultMigrationService.migrateVault({
        vaultRootPath: '/test/vault',
        templateType: 'default'
      })

      // Should fallback to rebuilding metadata
      expect(result.success).toBe(true)
      expect(result.restored).toBe(true)
    })
  })

  describe('Context ID Generation', () => {
    test('should generate new format context IDs during upgrade', async () => {
      mockElectronAPI.vault.pathExists.mockImplementation((path: string) => {
        if (path.includes('.chatlo')) return Promise.resolve({ exists: true })
        if (path.includes('vault-registry.json')) return Promise.resolve({ exists: true })
        return Promise.resolve({ exists: true })
      })

      // Mock registry with old format context IDs
      const mockRegistry = {
        version: '0.9.0',
        vaultRoot: '/test/vault',
        vaults: [
          {
            id: 'vault_personal_123',
            name: 'Personal Vault',
            path: '/test/vault/personal-vault',
            color: '#8AB0BB',
            icon: 'fa-user',
            created: '2024-01-01T00:00:00.000Z',
            lastAccessed: '2024-01-01T00:00:00.000Z',
            contexts: [
              {
                id: 'old_random_id', // Old format
                name: 'Test Context',
                path: '/test/vault/personal-vault/test-context',
                description: 'Test context',
                color: '#9CA3AF',
                icon: 'fa-folder',
                status: 'active'
              }
            ]
          }
        ],
        lastScan: '2024-01-01T00:00:00.000Z',
        preferences: {
          defaultVault: 'vault_personal_123',
          defaultContext: 'old_random_id',
          autoOrganize: true,
          showEmptyHints: true
        }
      }

      mockElectronAPI.vault.readFile.mockResolvedValue(JSON.stringify(mockRegistry))
      mockElectronAPI.vault.writeFile.mockResolvedValue({ success: true })
      mockElectronAPI.settings.set.mockResolvedValue(true)

      const result = await vaultMigrationService.migrateVault({
        vaultRootPath: '/test/vault',
        templateType: 'default'
      })

      expect(result.success).toBe(true)
      expect(result.restored).toBe(true)
      
      // Check that context ID was upgraded to new format
      const context = result.contexts[0]
      expect(context.id).toMatch(/^ctx_/)
    })
  })
})
