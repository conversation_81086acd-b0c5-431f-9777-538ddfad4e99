import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Folder, RefreshCw, AlertCircle, CheckCircle, Info, X } from '../components/Icons'
import { vaultMigrationService } from '../services/vaultMigrationService'
import { contextVaultService } from '../services/contextVaultService'
import { useArtifactToasts } from './artifacts/controls/ArtifactToast'

interface VaultMigrationSettingsProps {
  onVaultCreated?: () => void
}

export const VaultMigrationSettings: React.FC<VaultMigrationSettingsProps> = ({ onVaultCreated }) => {
  const navigate = useNavigate()
  const toasts = useArtifactToasts()

  // State management
  const [vaultRootPath, setVaultRootPath] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [showTemplateSelection, setShowTemplateSelection] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<'default' | 'simple'>('default')
  const [processingStep, setProcessingStep] = useState('')
  const [migrationResult, setMigrationResult] = useState<any>(null)
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false)
  const [forceRebuild, setForceRebuild] = useState(false)

  // Load existing vault root path on component mount
  useEffect(() => {
    loadExistingVaultRoot()
  }, [])

  /**
   * Load existing vault root path from settings
   */
  const loadExistingVaultRoot = async () => {
    try {
      if (window.electronAPI?.settings) {
        const savedVaultRoot = await window.electronAPI.settings.get('vault-root-path')
        if (savedVaultRoot) {
          setVaultRootPath(savedVaultRoot)
        }
      }
    } catch (error) {
      console.error('Failed to load existing vault root:', error)
    }
  }

  /**
   * Handle folder selection
   */
  const handleSelectFolder = async () => {
    try {
      if (!window.electronAPI?.files) {
        toasts.error('File system not available')
        return
      }

      const result = await window.electronAPI.files.showOpenDialog({
        title: 'Select Vault Root Folder',
        properties: ['openDirectory'],
        defaultPath: vaultRootPath || undefined
      })

      if (!result.canceled && result.filePaths.length > 0) {
        const selectedPath = result.filePaths[0]
        setVaultRootPath(selectedPath)
        
        // Check if folder has existing .chatlo
        await checkFolderStatus(selectedPath)
      }
    } catch (error: any) {
      console.error('Error selecting folder:', error)
      toasts.error(`Failed to select folder: ${error.message}`)
    }
  }

  /**
   * Check folder status and determine next steps
   */
  const checkFolderStatus = async (folderPath: string) => {
    try {
      setProcessingStep('Checking folder status...')
      
      // Check if .chatlo exists
      const chatloPath = `${folderPath}/.chatlo`
      const chatloExists = await window.electronAPI.vault.pathExists(chatloPath)

      if (chatloExists.exists) {
        // Existing vault detected
        setMigrationResult({
          type: 'existing',
          message: 'Existing vault detected! You can restore from previous setup or create new.',
          hasRegistry: await checkRegistryExists(folderPath)
        })
        setShowTemplateSelection(true)
      } else {
        // New folder
        setMigrationResult({
          type: 'new',
          message: 'New folder selected. Choose a template to create your vault structure.'
        })
        setShowTemplateSelection(true)
      }
    } catch (error: any) {
      console.error('Error checking folder status:', error)
      setMigrationResult({
        type: 'error',
        message: `Failed to check folder: ${error.message}`
      })
    } finally {
      setProcessingStep('')
    }
  }

  /**
   * Check if registry file exists
   */
  const checkRegistryExists = async (folderPath: string): Promise<boolean> => {
    try {
      const registryPath = `${folderPath}/.chatlo/vault-registry.json`
      const registryExists = await window.electronAPI.vault.pathExists(registryPath)
      return registryExists.exists
    } catch (error) {
      return false
    }
  }

  /**
   * Handle vault migration/creation
   */
  const handleMigrateVault = async () => {
    if (!vaultRootPath) {
      toasts.error('Please select a folder first')
      return
    }

    try {
      setIsProcessing(true)
      setProcessingStep('Starting vault migration...')

      const result = await vaultMigrationService.migrateVault({
        vaultRootPath,
        templateType: selectedTemplate,
        forceRebuild
      })

      if (result.success) {
        // Success handling
        const contextCount = result.contexts.length
        const vaultCount = result.vaults.length
        
        let successMessage = ''
        if (result.restored) {
          successMessage = `✅ Vault restored successfully! Found ${vaultCount} vault${vaultCount !== 1 ? 's' : ''} with ${contextCount} context${contextCount !== 1 ? 's' : ''}.`
        } else {
          successMessage = `✅ Vault created successfully! Created ${vaultCount} vault${vaultCount !== 1 ? 's' : ''} with ${contextCount} context${contextCount !== 1 ? 's' : ''}.`
        }

        // Show warnings if any
        if (result.warnings && result.warnings.length > 0) {
          successMessage += `\n\nWarnings:\n${result.warnings.join('\n')}`
        }

        toasts.success(successMessage, 8000)
        
        setMigrationResult({
          type: 'success',
          message: successMessage,
          vaults: result.vaults,
          contexts: result.contexts,
          restored: result.restored,
          warnings: result.warnings
        })

        // Reset form state
        setShowTemplateSelection(false)
        setForceRebuild(false)

        // Notify parent component
        onVaultCreated?.()

        // Navigate to files page after delay
        setTimeout(() => {
          navigate('/files')
        }, 2000)

      } else {
        // Error handling
        const errorMessage = `❌ Vault migration failed: ${result.error}`
        toasts.error(errorMessage)
        
        setMigrationResult({
          type: 'error',
          message: errorMessage
        })
      }

    } catch (error: any) {
      console.error('Vault migration error:', error)
      const errorMessage = `❌ Unexpected error: ${error.message}`
      toasts.error(errorMessage)
      
      setMigrationResult({
        type: 'error',
        message: errorMessage
      })
    } finally {
      setIsProcessing(false)
      setProcessingStep('')
      
      // Clear result after delay
      setTimeout(() => {
        setMigrationResult(null)
      }, 10000)
    }
  }

  /**
   * Get status icon based on migration result
   */
  const getStatusIcon = () => {
    if (!migrationResult) return null

    switch (migrationResult.type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-400" />
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-400" />
      case 'existing':
        return <Info className="w-5 h-5 text-blue-400" />
      case 'new':
        return <Info className="w-5 h-5 text-yellow-400" />
      default:
        return <Info className="w-5 h-5 text-gray-400" />
    }
  }

  /**
   * Get status color classes
   */
  const getStatusClasses = () => {
    if (!migrationResult) return ''

    switch (migrationResult.type) {
      case 'success':
        return 'bg-green-900/20 border-green-500/50 text-green-300'
      case 'error':
        return 'bg-red-900/20 border-red-500/50 text-red-300'
      case 'existing':
        return 'bg-blue-900/20 border-blue-500/50 text-blue-300'
      case 'new':
        return 'bg-yellow-900/20 border-yellow-500/50 text-yellow-300'
      default:
        return 'bg-gray-900/20 border-gray-500/50 text-gray-300'
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-semibold mb-2 text-supplement1">File Vault Setup</h2>
        <p className="text-gray-400">
          Select a folder to create or restore your ChatLo vault system. The system will automatically detect existing vaults and offer restoration options.
        </p>
      </div>

      {/* Folder Selection */}
      <div className="u1-card bg-gray-800/50 border border-gray-700">
        <h3 className="text-lg font-medium mb-4">Vault Root Location</h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Select Vault Folder
            </label>
            <div className="flex gap-2">
              <input
                type="text"
                value={vaultRootPath}
                readOnly
                placeholder="No folder selected"
                className="flex-1 bg-neutral-800 border border-neutral-700 rounded-lg px-3 py-2 text-sm font-mono text-neutral-300"
              />
              <button
                onClick={handleSelectFolder}
                disabled={isProcessing}
                className="px-3 py-2 bg-neutral-700 hover:bg-neutral-600 text-white rounded-lg text-sm font-medium transition-colors flex items-center gap-2 disabled:opacity-50"
              >
                <Folder size={16} />
                {isProcessing ? 'Processing...' : 'Browse'}
              </button>
            </div>
          </div>

          {/* Processing Status */}
          {processingStep && (
            <div className="flex items-center gap-2 text-sm text-blue-400">
              <RefreshCw className="w-4 h-4 animate-spin" />
              {processingStep}
            </div>
          )}

          {/* Migration Result */}
          {migrationResult && (
            <div className={`p-3 rounded-lg border flex items-start gap-3 ${getStatusClasses()}`}>
              {getStatusIcon()}
              <div className="flex-1">
                <div className="text-sm whitespace-pre-line">{migrationResult.message}</div>
                
                {/* Show additional info for existing vaults */}
                {migrationResult.type === 'existing' && (
                  <div className="mt-2 text-xs opacity-75">
                    Registry file: {migrationResult.hasRegistry ? '✅ Found' : '❌ Missing (will rebuild)'}
                  </div>
                )}

                {/* Show warnings */}
                {migrationResult.warnings && migrationResult.warnings.length > 0 && (
                  <div className="mt-2">
                    <div className="text-xs font-medium mb-1">Warnings:</div>
                    <ul className="text-xs opacity-75 space-y-1">
                      {migrationResult.warnings.map((warning: string, index: number) => (
                        <li key={index}>• {warning}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
              
              {/* Close button */}
              <button
                onClick={() => setMigrationResult(null)}
                className="text-current opacity-50 hover:opacity-100 transition-opacity"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Template Selection */}
      {showTemplateSelection && vaultRootPath && (
        <div className="u1-card bg-gray-800/50 border border-gray-700">
          <h3 className="text-lg font-medium mb-4">
            {migrationResult?.type === 'existing' ? 'Restoration Options' : 'Vault Template'}
          </h3>

          <div className="space-y-4">
            {/* Template Options */}
            <div className="space-y-3">
              <div
                className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                  selectedTemplate === 'default'
                    ? 'bg-blue-900/20 border-blue-500/50'
                    : 'bg-gray-700 border-gray-600 hover:bg-gray-600'
                }`}
                onClick={() => setSelectedTemplate('default')}
              >
                <div className="flex items-center gap-3">
                  <input
                    type="radio"
                    checked={selectedTemplate === 'default'}
                    onChange={() => setSelectedTemplate('default')}
                    className="text-blue-500"
                  />
                  <div>
                    <div className="font-medium">Default Setup</div>
                    <div className="text-sm text-gray-400">
                      Personal & Work vaults with shared dropbox
                    </div>
                  </div>
                </div>
              </div>

              <div
                className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                  selectedTemplate === 'simple'
                    ? 'bg-blue-900/20 border-blue-500/50'
                    : 'bg-gray-700 border-gray-600 hover:bg-gray-600'
                }`}
                onClick={() => setSelectedTemplate('simple')}
              >
                <div className="flex items-center gap-3">
                  <input
                    type="radio"
                    checked={selectedTemplate === 'simple'}
                    onChange={() => setSelectedTemplate('simple')}
                    className="text-blue-500"
                  />
                  <div>
                    <div className="font-medium">Simple Setup</div>
                    <div className="text-sm text-gray-400">
                      Single personal vault to get started
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Advanced Options */}
            {migrationResult?.type === 'existing' && (
              <div className="border-t border-gray-600 pt-4">
                <button
                  onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                  className="text-sm text-blue-400 hover:text-blue-300 transition-colors"
                >
                  {showAdvancedOptions ? 'Hide' : 'Show'} Advanced Options
                </button>

                {showAdvancedOptions && (
                  <div className="mt-3 space-y-3">
                    <label className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={forceRebuild}
                        onChange={(e) => setForceRebuild(e.target.checked)}
                        className="rounded"
                      />
                      <span className="text-sm">
                        Force rebuild metadata (ignore existing registry)
                      </span>
                    </label>
                    <p className="text-xs text-gray-400">
                      Use this option if you're experiencing issues with vault restoration.
                      All files and folders will be preserved, but metadata will be rebuilt.
                    </p>
                  </div>
                )}
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4">
              <button
                onClick={handleMigrateVault}
                disabled={isProcessing}
                className="u1-button-primary disabled:opacity-50 flex items-center gap-2"
              >
                {isProcessing && <RefreshCw className="w-4 h-4 animate-spin" />}
                {migrationResult?.type === 'existing' 
                  ? (forceRebuild ? 'Rebuild Vault' : 'Restore Vault')
                  : 'Create Vault'
                }
              </button>

              <button
                onClick={() => {
                  setShowTemplateSelection(false)
                  setMigrationResult(null)
                  setForceRebuild(false)
                }}
                disabled={isProcessing}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded-lg text-sm font-medium transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
