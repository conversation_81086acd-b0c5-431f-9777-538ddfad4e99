/**
 * ChatLo Admin Dashboard
 * Main admin interface with navigation to various management pages
 */

import React, { useState } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faTachometerAlt,
  faKey,
  faUsers,
  faChartBar,
  faShieldAlt,
  faCog,
  faDatabase,
  faFileAlt,
  faBars,
  faTimes
} from '@fortawesome/free-solid-svg-icons'
import { APIKeysPage } from './APIKeysPage'
import { ServiceAccessDashboard } from '../../components/ServiceAccessDashboard'

type AdminPage = 'overview' | 'api-keys' | 'access-logs' | 'users' | 'analytics' | 'security' | 'settings' | 'database'

export const AdminDashboard: React.FC = () => {
  const [currentPage, setCurrentPage] = useState<AdminPage>('overview')
  const [sidebarOpen, setSidebarOpen] = useState(true)

  const navigationItems = [
    { id: 'overview', label: 'Overview', icon: faTachometerAlt },
    { id: 'api-keys', label: 'API Keys', icon: faKey },
    { id: 'access-logs', label: 'Access Logs', icon: faFileAlt },
    { id: 'users', label: 'Users', icon: faUsers },
    { id: 'analytics', label: 'Analytics', icon: faChartBar },
    { id: 'security', label: 'Security', icon: faShieldAlt },
    { id: 'database', label: 'Database', icon: faDatabase },
    { id: 'settings', label: 'Settings', icon: faCog }
  ] as const

  const renderPageContent = () => {
    switch (currentPage) {
      case 'api-keys':
        return <APIKeysPage />
      case 'access-logs':
        return <ServiceAccessDashboard />
      case 'overview':
        return <AdminOverview />
      case 'users':
        return <div className="p-6"><h2 className="text-2xl font-bold">User Management</h2><p className="text-gray-600 mt-2">Coming soon...</p></div>
      case 'analytics':
        return <div className="p-6"><h2 className="text-2xl font-bold">Analytics</h2><p className="text-gray-600 mt-2">Coming soon...</p></div>
      case 'security':
        return <div className="p-6"><h2 className="text-2xl font-bold">Security Settings</h2><p className="text-gray-600 mt-2">Coming soon...</p></div>
      case 'database':
        return <div className="p-6"><h2 className="text-2xl font-bold">Database Management</h2><p className="text-gray-600 mt-2">Coming soon...</p></div>
      case 'settings':
        return <div className="p-6"><h2 className="text-2xl font-bold">System Settings</h2><p className="text-gray-600 mt-2">Coming soon...</p></div>
      default:
        return <AdminOverview />
    }
  }

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <div className={`${sidebarOpen ? 'w-64' : 'w-16'} bg-white shadow-lg transition-all duration-300 flex flex-col`}>
        {/* Sidebar Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            {sidebarOpen && (
              <h1 className="text-xl font-bold text-gray-900">ChatLo Admin</h1>
            )}
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-2 rounded-md hover:bg-gray-100"
            >
              <FontAwesomeIcon icon={sidebarOpen ? faTimes : faBars} />
            </button>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            {navigationItems.map((item) => (
              <li key={item.id}>
                <button
                  onClick={() => setCurrentPage(item.id as AdminPage)}
                  className={`w-full flex items-center px-3 py-2 rounded-md text-left transition-colors ${
                    currentPage === item.id
                      ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-700'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <FontAwesomeIcon icon={item.icon} className="w-5 h-5" />
                  {sidebarOpen && (
                    <span className="ml-3 font-medium">{item.label}</span>
                  )}
                </button>
              </li>
            ))}
          </ul>
        </nav>

        {/* Sidebar Footer */}
        <div className="p-4 border-t border-gray-200">
          {sidebarOpen && (
            <div className="text-xs text-gray-500">
              <p>ChatLo Admin Dashboard</p>
              <p>Version 1.0.0</p>
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        {renderPageContent()}
      </div>
    </div>
  )
}

const AdminOverview: React.FC = () => {
  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
        <p className="text-gray-600 mt-2">
          Welcome to the ChatLo administration interface. Manage API keys, monitor access, and configure system settings.
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faKey} className="text-blue-600 text-2xl" />
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-gray-900">API Keys</h3>
              <p className="text-2xl font-bold text-blue-600">12</p>
              <p className="text-sm text-gray-500">Active services</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faFileAlt} className="text-green-600 text-2xl" />
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-gray-900">API Calls</h3>
              <p className="text-2xl font-bold text-green-600">1,247</p>
              <p className="text-sm text-gray-500">Last 24 hours</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faShieldAlt} className="text-yellow-600 text-2xl" />
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-gray-900">Security</h3>
              <p className="text-2xl font-bold text-yellow-600">98.5%</p>
              <p className="text-sm text-gray-500">Success rate</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faDatabase} className="text-purple-600 text-2xl" />
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-gray-900">Storage</h3>
              <p className="text-2xl font-bold text-purple-600">2.3 GB</p>
              <p className="text-sm text-gray-500">Files processed</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow border">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <button
              onClick={() => {/* Navigate to API Keys */}}
              className="w-full flex items-center justify-between p-3 bg-blue-50 hover:bg-blue-100 rounded-md transition-colors"
            >
              <div className="flex items-center">
                <FontAwesomeIcon icon={faKey} className="text-blue-600 mr-3" />
                <span className="font-medium">Create New API Key</span>
              </div>
              <span className="text-blue-600">→</span>
            </button>
            
            <button
              onClick={() => {/* Navigate to Access Logs */}}
              className="w-full flex items-center justify-between p-3 bg-green-50 hover:bg-green-100 rounded-md transition-colors"
            >
              <div className="flex items-center">
                <FontAwesomeIcon icon={faFileAlt} className="text-green-600 mr-3" />
                <span className="font-medium">View Access Logs</span>
              </div>
              <span className="text-green-600">→</span>
            </button>
            
            <button
              onClick={() => {/* Navigate to Security */}}
              className="w-full flex items-center justify-between p-3 bg-yellow-50 hover:bg-yellow-100 rounded-md transition-colors"
            >
              <div className="flex items-center">
                <FontAwesomeIcon icon={faShieldAlt} className="text-yellow-600 mr-3" />
                <span className="font-medium">Security Settings</span>
              </div>
              <span className="text-yellow-600">→</span>
            </button>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between py-2 border-b border-gray-100">
              <div>
                <p className="text-sm font-medium text-gray-900">New API key created</p>
                <p className="text-xs text-gray-500">FileProcessor Pro v1.2.0</p>
              </div>
              <span className="text-xs text-gray-400">2 min ago</span>
            </div>
            
            <div className="flex items-center justify-between py-2 border-b border-gray-100">
              <div>
                <p className="text-sm font-medium text-gray-900">Service access revoked</p>
                <p className="text-xs text-gray-500">TestPlugin v0.1.0</p>
              </div>
              <span className="text-xs text-gray-400">1 hour ago</span>
            </div>
            
            <div className="flex items-center justify-between py-2 border-b border-gray-100">
              <div>
                <p className="text-sm font-medium text-gray-900">High API usage detected</p>
                <p className="text-xs text-gray-500">BackupService v3.0.0</p>
              </div>
              <span className="text-xs text-gray-400">3 hours ago</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
