# Context Management Service API Documentation

## Overview

The Context Management Service is the second heart of the ChatLo system, responsible for context creation, management, and lifecycle operations. It implements a portable vault design with carry-on capability, allowing contexts to be detached and reattached across different installations while preserving all data and history.

## Core Design Principles

### 1. Portable & Carry-On Design
- **Self-Contained**: Each context contains all metadata and settings within its folder structure
- **Detachable**: Contexts can be moved to USB drives or other storage without losing functionality
- **Reattachable**: Contexts can be restored to any ChatLo installation with full history preservation
- **Version Compatible**: Contexts maintain compatibility information for seamless migration

### 2. Flexible Vault Structure
The service supports both default and simple setup modes:

#### Default Setup
```
File Vault/
├── .chatlo/                    # System files
│   └── vault-registry.json
├── shared-dropbox/             # Raw data storage
│   ├── .metadata.json
│   └── [uploaded files]
├── personal-vault/
│   └── your-first-personal-context/
│       ├── .context/           # Context metadata
│       │   ├── metadata.json
│       │   ├── manifest.json
│       │   └── settings.json
│       ├── documents/
│       ├── images/
│       ├── artifacts/
│       └── master.md
└── work-vault/
    └── your-first-project-context/
        ├── .context/
        ├── documents/
        ├── images/
        ├── artifacts/
        └── master.md
```

#### Simple Setup
```
File Vault/
├── .chatlo/
├── shared-dropbox/
└── personal-vault/
    └── your-first-personal-context/
        ├── .context/
        ├── documents/
        ├── images/
        ├── artifacts/
        └── master.md
```

### 3. Smart Context ID Management
- **Stable IDs**: Generated using name + timestamp + random component
- **Human Readable**: IDs include context name for easy identification
- **Collision Resistant**: Timestamp and random components prevent conflicts
- **Format**: `ctx_{clean-name}_{timestamp}_{random}`

## API Reference

### Context Creation

#### `createContext(options: ContextCreationOptions)`

Creates a new context with full metadata and folder structure.

```typescript
interface ContextCreationOptions {
  name: string
  description?: string
  vaultType: 'Personal' | 'Work' | 'Custom'
  vaultPath?: string // For custom vault location
  template?: 'default' | 'project' | 'research' | 'personal' | 'minimal'
  color?: string
  icon?: string
  metadata?: Record<string, any>
}

// Example usage
const result = await contextManagementService.createContext({
  name: "Machine Learning Research",
  description: "Research project on neural networks",
  vaultType: "Work",
  template: "research",
  color: "#4F46E5",
  icon: "fa-brain"
})
```

**Response:**
```typescript
{
  success: boolean
  context?: ContextFolder
  error?: string
}
```

### Vault Setup

#### `setupVaultStructure(options: VaultSetupOptions)`

Initializes the complete vault structure based on user preferences.

```typescript
interface VaultSetupOptions {
  setupType: 'default' | 'simple'
  vaultRoot: string
  customVaults?: Array<{
    name: string
    path: string
    color: string
    icon: string
  }>
}

// Example usage
const result = await contextManagementService.setupVaultStructure({
  setupType: 'default',
  vaultRoot: '/Users/<USER>/Documents/ChatLo_Vaults',
  customVaults: [
    {
      name: "Research Vault",
      path: "research-vault",
      color: "#10B981",
      icon: "fa-microscope"
    }
  ]
})
```

## Context Metadata Structure

Each context maintains comprehensive metadata for portability and management:

```typescript
interface ContextMetadata {
  id: string                    // Stable context identifier
  name: string                  // Human-readable name
  description: string           // Context description
  created: string              // ISO timestamp
  lastModified: string         // ISO timestamp
  version: string              // Context format version
  contextType: string          // Template type used
  color: string                // UI color theme
  icon: string                 // UI icon
  tags: string[]               // User-defined tags
  settings: {
    autoOrganize: boolean
    fileRouting: {
      documents: string
      images: string
      artifacts: string
    }
    aiInsights: boolean
    syncEnabled: boolean
  }
  statistics: {
    fileCount: number
    totalSize: number
    conversationCount: number
    lastActivity: string
  }
  portability: {
    isPortable: boolean
    exportVersion: string
    dependencies: string[]
    attachedAt: string
    detachedAt?: string
  }
}
```

## Context Templates

### Default Template
Basic context with standard folder structure and generic master document.

### Project Template
Optimized for project management with additional folders:
- `/planning` - Project planning documents
- `/resources` - Project resources and assets

Master document includes:
- Project status tracking
- Objectives checklist
- Resource organization
- Next steps planning

### Research Template
Designed for research activities with specialized folders:
- `/references` - Academic papers and sources
- `/notes` - Research notes and observations

Master document includes:
- Research questions
- Methodology section
- Findings documentation
- Source tracking

### Personal Template
Tailored for personal use with focus on individual productivity.

### Minimal Template
Stripped-down version with only essential folders and basic master document.

## File Routing Integration

The Context Management Service integrates seamlessly with the File Routing Service:

1. **Auto-routing**: Files are automatically routed to appropriate folders based on MIME type
2. **Context-aware**: File routing respects context-specific settings
3. **Customizable**: Each context can override default routing rules

```typescript
// Context-specific file routing settings
{
  fileRouting: {
    documents: "documents",    // Text files, PDFs
    images: "images",         // Image files
    artifacts: "artifacts"    // Generated content, exports
  }
}
```

## Portability Features

### Context Export
Contexts are designed to be fully portable:

1. **Self-contained**: All metadata stored within context folder
2. **Manifest file**: Documents context structure and requirements
3. **Version tracking**: Ensures compatibility across ChatLo versions
4. **Dependency tracking**: Records any external dependencies

### Context Import/Reattach
When reattaching a context to a new ChatLo installation:

1. **Validation**: Checks manifest and compatibility
2. **Structure verification**: Ensures all required files and folders exist
3. **Metadata restoration**: Restores all context settings and history
4. **Integration**: Registers context with new vault system

## Usage Examples

### Creating a Research Context
```typescript
const researchContext = await contextManagementService.createContext({
  name: "AI Ethics Study",
  description: "Research on ethical implications of AI systems",
  vaultType: "Work",
  template: "research",
  color: "#8B5CF6",
  icon: "fa-balance-scale"
})

if (researchContext.success) {
  console.log(`Created context: ${researchContext.context.id}`)
  // Context is ready with /references and /notes folders
  // Master document includes research template
}
```

### Setting Up Simple Vault Structure
```typescript
const vaultSetup = await contextManagementService.setupVaultStructure({
  setupType: 'simple',
  vaultRoot: '/Users/<USER>/Documents/MyVault'
})

if (vaultSetup.success) {
  console.log(`Created ${vaultSetup.vaults.length} vaults`)
  // Single personal vault with default context created
}
```

### Creating Custom Project Context
```typescript
const projectContext = await contextManagementService.createContext({
  name: "Website Redesign",
  description: "Complete redesign of company website",
  vaultType: "Work",
  template: "project",
  color: "#F59E0B",
  icon: "fa-paint-brush",
  metadata: {
    client: "Acme Corp",
    deadline: "2024-03-15",
    budget: "$50,000"
  }
})
```

## Integration with Existing Services

### File Routing Service
- Contexts register their file routing preferences
- Auto-routing respects context-specific settings
- File operations are logged in context statistics

### Context Vault Service
- New contexts are automatically registered in vault registry
- Context selection triggers appropriate UI updates
- Vault scanning includes context metadata validation

### Shared Dropbox Service
- Shared dropbox remains separate from context structure
- Files can be moved from shared dropbox to specific contexts
- Context creation includes shared dropbox setup

## Error Handling

The service provides comprehensive error handling:

```typescript
// Common error scenarios
try {
  const result = await contextManagementService.createContext(options)
  if (!result.success) {
    switch (result.error) {
      case 'Context already exists':
        // Handle duplicate context
        break
      case 'Target vault not found':
        // Handle missing vault
        break
      case 'Invalid context name':
        // Handle validation error
        break
    }
  }
} catch (error) {
  // Handle service errors
  console.error('Context creation failed:', error)
}
```

## Future Enhancements

### Phase 2 Features
1. **Context Templates**: User-defined custom templates
2. **Context Cloning**: Duplicate existing contexts with customization
3. **Bulk Operations**: Create multiple contexts from templates
4. **Context Archiving**: Archive inactive contexts while preserving data

### Phase 3 Features
1. **Context Synchronization**: Sync contexts across devices
2. **Collaborative Contexts**: Multi-user context sharing
3. **Context Analytics**: Advanced usage statistics and insights
4. **Template Marketplace**: Share and download community templates

This Context Management Service provides the foundation for a flexible, portable, and user-friendly context system that grows with user needs while maintaining simplicity and reliability.
