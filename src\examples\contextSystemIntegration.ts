/**
 * Context System Integration Example
 * Demonstrates how File Routing Service and Context Management Service work together
 */

import { fileRoutingService } from '../services/fileRoutingService'
import { contextManagementService } from '../services/contextManagementService'
import { contextVaultService } from '../services/contextVaultService'

/**
 * Example 1: Complete Vault Setup and Context Creation
 */
export async function setupCompleteVaultSystem() {
  console.log('🚀 Setting up complete vault system...')

  try {
    // Step 1: Initialize vault structure
    const vaultSetup = await contextManagementService.setupVaultStructure({
      setupType: 'default',
      vaultRoot: '/Users/<USER>/Documents/ChatLo_Demo_Vault',
      customVaults: [
        {
          name: 'Research Vault',
          path: 'research-vault',
          color: '#10B981',
          icon: 'fa-microscope'
        }
      ]
    })

    if (!vaultSetup.success) {
      throw new Error(`Vault setup failed: ${vaultSetup.error}`)
    }

    console.log(`✅ Created ${vaultSetup.vaults.length} vaults`)

    // Step 2: Create specialized research context
    const researchContext = await contextManagementService.createContext({
      name: 'AI Ethics Research',
      description: 'Research project on ethical implications of AI systems',
      vaultType: 'Custom',
      vaultPath: '/Users/<USER>/Documents/ChatLo_Demo_Vault/research-vault',
      template: 'research',
      color: '#8B5CF6',
      icon: 'fa-balance-scale',
      metadata: {
        researchArea: 'AI Ethics',
        institution: 'University Demo',
        startDate: '2024-01-15'
      }
    })

    if (!researchContext.success) {
      throw new Error(`Research context creation failed: ${researchContext.error}`)
    }

    console.log(`✅ Created research context: ${researchContext.context?.id}`)

    // Step 3: Create project context in work vault
    const projectContext = await contextManagementService.createContext({
      name: 'Website Redesign Project',
      description: 'Complete redesign of company website with modern UI/UX',
      vaultType: 'Work',
      template: 'project',
      color: '#F59E0B',
      icon: 'fa-paint-brush',
      metadata: {
        client: 'Acme Corporation',
        deadline: '2024-03-15',
        budget: '$50,000',
        team: ['designer', 'developer', 'pm']
      }
    })

    if (!projectContext.success) {
      throw new Error(`Project context creation failed: ${projectContext.error}`)
    }

    console.log(`✅ Created project context: ${projectContext.context?.id}`)

    return {
      vaults: vaultSetup.vaults,
      contexts: [researchContext.context, projectContext.context].filter(Boolean)
    }

  } catch (error) {
    console.error('❌ Vault system setup failed:', error)
    throw error
  }
}

/**
 * Example 2: File Upload with Context Integration
 */
export async function demonstrateFileUploadFlow() {
  console.log('📁 Demonstrating file upload flow...')

  try {
    // Step 1: Load existing vaults and contexts
    const vaults = await contextVaultService.loadVaults()
    console.log(`📂 Loaded ${vaults.length} vaults`)

    // Step 2: Find a research context to upload to
    const allContexts = contextVaultService.getAllContexts()
    const researchContext = allContexts.find(ctx => 
      ctx.aiInsights.contextType === 'research'
    )

    if (!researchContext) {
      throw new Error('No research context found for demo')
    }

    console.log(`🎯 Selected context: ${researchContext.name} (${researchContext.id})`)

    // Step 3: Simulate file uploads with different types
    const mockFiles = [
      createMockFile('research_paper.pdf', 'application/pdf', 2048000),
      createMockFile('data_visualization.png', 'image/png', 512000),
      createMockFile('analysis_notes.txt', 'text/plain', 8192),
      createMockFile('dataset.json', 'application/json', 1024000)
    ]

    // Step 4: Upload files to research context with auto-routing
    const uploadResult = await fileRoutingService.uploadToContext(
      mockFiles,
      researchContext.id
    )

    if (!uploadResult.success) {
      throw new Error(`File upload failed: ${uploadResult.errors?.[0]?.message}`)
    }

    console.log(`✅ Uploaded ${uploadResult.files.length} files:`)
    uploadResult.files.forEach(file => {
      console.log(`   📄 ${file.originalName} → ${file.savedPath}`)
    })

    // Step 5: Demonstrate shared dropbox upload
    const sharedFiles = [
      createMockFile('unclassified_document.docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 256000),
      createMockFile('random_image.jpg', 'image/jpeg', 1024000)
    ]

    const sharedUploadResult = await fileRoutingService.handleFileDrop(
      sharedFiles,
      { autoRoute: true } // No contextId = shared dropbox
    )

    if (!sharedUploadResult.success) {
      throw new Error(`Shared upload failed: ${sharedUploadResult.errors?.[0]?.message}`)
    }

    console.log(`✅ Uploaded ${sharedUploadResult.files.length} files to shared dropbox:`)
    sharedUploadResult.files.forEach(file => {
      console.log(`   📄 ${file.originalName} → ${file.savedPath}`)
    })

    return {
      contextUploads: uploadResult.files,
      sharedUploads: sharedUploadResult.files
    }

  } catch (error) {
    console.error('❌ File upload demonstration failed:', error)
    throw error
  }
}

/**
 * Example 3: Context Portability Demonstration
 */
export async function demonstrateContextPortability() {
  console.log('🎒 Demonstrating context portability...')

  try {
    // Step 1: Create a portable context
    const portableContext = await contextManagementService.createContext({
      name: 'Portable Research Notes',
      description: 'Research notes that can be carried on USB drive',
      vaultType: 'Personal',
      template: 'research',
      color: '#06B6D4',
      icon: 'fa-usb',
      metadata: {
        portable: true,
        created_for: 'USB carry demo'
      }
    })

    if (!portableContext.success) {
      throw new Error(`Portable context creation failed: ${portableContext.error}`)
    }

    console.log(`✅ Created portable context: ${portableContext.context?.id}`)

    // Step 2: Add some files to make it realistic
    const contextFiles = [
      createMockFile('research_methodology.md', 'text/markdown', 4096),
      createMockFile('literature_review.pdf', 'application/pdf', 1048576),
      createMockFile('experiment_data.csv', 'text/csv', 32768)
    ]

    const fileUpload = await fileRoutingService.uploadToContext(
      contextFiles,
      portableContext.context!.id
    )

    if (!fileUpload.success) {
      throw new Error(`File upload to portable context failed`)
    }

    console.log(`✅ Added ${fileUpload.files.length} files to portable context`)

    // Step 3: Simulate context export/copy process
    console.log('📦 Context is ready for portability:')
    console.log(`   📁 Context folder: ${portableContext.context?.path}`)
    console.log(`   📄 Contains metadata, manifest, and all files`)
    console.log(`   🔄 Can be copied to USB drive or cloud storage`)
    console.log(`   ✨ Will work on any ChatLo installation`)

    // Step 4: Show what files would be included in portable package
    const contextPath = portableContext.context?.path
    console.log('\n📋 Portable package contents:')
    console.log(`   ${contextPath}/.context/metadata.json     # Context metadata`)
    console.log(`   ${contextPath}/.context/manifest.json     # Portability manifest`)
    console.log(`   ${contextPath}/.context/settings.json     # Context settings`)
    console.log(`   ${contextPath}/master.md                  # Master document`)
    console.log(`   ${contextPath}/documents/                 # Document files`)
    console.log(`   ${contextPath}/images/                    # Image files`)
    console.log(`   ${contextPath}/artifacts/                 # Generated content`)
    console.log(`   ${contextPath}/references/                # Research references`)
    console.log(`   ${contextPath}/notes/                     # Research notes`)

    return {
      portableContext: portableContext.context,
      uploadedFiles: fileUpload.files
    }

  } catch (error) {
    console.error('❌ Context portability demonstration failed:', error)
    throw error
  }
}

/**
 * Helper function to create mock File objects for demonstration
 */
function createMockFile(name: string, type: string, size: number): File {
  const content = new Array(size).fill('a').join('')
  const blob = new Blob([content], { type })
  
  // Create a File object with the blob
  const file = new File([blob], name, { type })
  
  return file
}

/**
 * Complete integration demonstration
 */
export async function runCompleteDemo() {
  console.log('🎬 Starting complete ChatLo context system demonstration...\n')

  try {
    // Phase 1: Setup vault system
    console.log('=== Phase 1: Vault System Setup ===')
    const setupResult = await setupCompleteVaultSystem()
    console.log(`✅ Setup complete: ${setupResult.vaults.length} vaults, ${setupResult.contexts.length} contexts\n`)

    // Phase 2: File upload demonstration
    console.log('=== Phase 2: File Upload Flow ===')
    const uploadResult = await demonstrateFileUploadFlow()
    console.log(`✅ Upload complete: ${uploadResult.contextUploads.length} context files, ${uploadResult.sharedUploads.length} shared files\n`)

    // Phase 3: Portability demonstration
    console.log('=== Phase 3: Context Portability ===')
    const portabilityResult = await demonstrateContextPortability()
    console.log(`✅ Portability demo complete: Context ready for USB carry\n`)

    console.log('🎉 Complete demonstration finished successfully!')
    console.log('\n📊 Summary:')
    console.log(`   • Vaults created: ${setupResult.vaults.length}`)
    console.log(`   • Contexts created: ${setupResult.contexts.length + 1}`)
    console.log(`   • Files uploaded: ${uploadResult.contextUploads.length + uploadResult.sharedUploads.length}`)
    console.log(`   • Portable contexts: 1`)

    return {
      setup: setupResult,
      uploads: uploadResult,
      portability: portabilityResult
    }

  } catch (error) {
    console.error('❌ Complete demonstration failed:', error)
    throw error
  }
}

// Export for use in other parts of the application
export {
  createMockFile
}
