/**
 * API Keys Management Page for ChatLo Admin Dashboard
 * Manages service registration, API key creation, and access monitoring
 */

import React, { useState, useEffect } from 'react'
import { serviceAccessLogger } from '../../services/serviceAccessLogger'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faKey,
  faPlus,
  faEye,
  faEyeSlash,
  faCopy,
  faTrash,
  faExclamationTriangle,
  faCheckCircle,
  faTimesCircle,
  faSpinner
} from '@fortawesome/free-solid-svg-icons'

interface APIKeyData {
  id: string
  name: string
  version: string
  apiKey: string
  capabilities: string[]
  developerInfo: {
    name: string
    email: string
    organization?: string
  }
  status: 'active' | 'suspended' | 'revoked'
  createdAt: Date
  lastAccess?: Date
  totalRequests: number
  successRate: number
}

interface NewServiceForm {
  name: string
  version: string
  description: string
  developerName: string
  developerEmail: string
  organization: string
  capabilities: string[]
}

export const APIKeysPage: React.FC = () => {
  const [apiKeys, setApiKeys] = useState<APIKeyData[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [newService, setNewService] = useState<NewServiceForm>({
    name: '',
    version: '1.0.0',
    description: '',
    developerName: '',
    developerEmail: '',
    organization: '',
    capabilities: []
  })
  const [createdKey, setCreatedKey] = useState<{ serviceId: string; apiKey: string } | null>(null)
  const [visibleKeys, setVisibleKeys] = useState<Set<string>>(new Set())

  const availableCapabilities = [
    'file.upload',
    'file.read',
    'file.list',
    'file.delete',
    'file.process',
    'context.read',
    'context.write',
    'admin.read'
  ]

  useEffect(() => {
    loadAPIKeys()
  }, [])

  const loadAPIKeys = async () => {
    try {
      setLoading(true)
      const services = await serviceAccessLogger.getServiceAccessReport()
      setApiKeys(services.map(mapServiceToAPIKey))
    } catch (error) {
      console.error('Failed to load API keys:', error)
    } finally {
      setLoading(false)
    }
  }

  const mapServiceToAPIKey = (service: any): APIKeyData => ({
    id: service.id,
    name: service.name,
    version: service.version,
    apiKey: '••••••••••••••••', // Masked for security
    capabilities: service.capabilities || [],
    developerInfo: service.developer_info || { name: 'Unknown', email: '<EMAIL>' },
    status: service.status,
    createdAt: new Date(service.created_at),
    lastAccess: service.last_access ? new Date(service.last_access) : undefined,
    totalRequests: service.total_requests || 0,
    successRate: service.successful_requests && service.total_requests 
      ? (service.successful_requests / service.total_requests) * 100 
      : 0
  })

  const handleCreateAPIKey = async () => {
    try {
      // Generate API key
      const apiKey = generateAPIKey()
      const serviceId = await serviceAccessLogger.registerService({
        name: newService.name,
        version: newService.version,
        apiKeyHash: await hashAPIKey(apiKey),
        capabilities: newService.capabilities,
        developerInfo: {
          name: newService.developerName,
          email: newService.developerEmail,
          organization: newService.organization || undefined
        },
        status: 'active'
      })

      // Show the generated key (only once)
      setCreatedKey({ serviceId, apiKey })
      
      // Reset form
      setNewService({
        name: '',
        version: '1.0.0',
        description: '',
        developerName: '',
        developerEmail: '',
        organization: '',
        capabilities: []
      })
      setShowCreateForm(false)
      
      // Reload the list
      await loadAPIKeys()
    } catch (error) {
      console.error('Failed to create API key:', error)
      alert('Failed to create API key. Please try again.')
    }
  }

  const handleRevokeKey = async (serviceId: string, serviceName: string) => {
    if (confirm(`Are you sure you want to revoke API access for "${serviceName}"? This action cannot be undone.`)) {
      try {
        await serviceAccessLogger.revokeService(serviceId, 'Revoked by admin')
        await loadAPIKeys()
      } catch (error) {
        console.error('Failed to revoke API key:', error)
        alert('Failed to revoke API key. Please try again.')
      }
    }
  }

  const toggleKeyVisibility = (keyId: string) => {
    const newVisible = new Set(visibleKeys)
    if (newVisible.has(keyId)) {
      newVisible.delete(keyId)
    } else {
      newVisible.add(keyId)
    }
    setVisibleKeys(newVisible)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    // You could add a toast notification here
  }

  const generateAPIKey = (): string => {
    return `ak_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`
  }

  const hashAPIKey = async (apiKey: string): Promise<string> => {
    // In a real implementation, use proper hashing like bcrypt
    return btoa(apiKey) // Simple base64 for demo
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <FontAwesomeIcon icon={faCheckCircle} className="text-green-500" />
      case 'suspended': return <FontAwesomeIcon icon={faExclamationTriangle} className="text-yellow-500" />
      case 'revoked': return <FontAwesomeIcon icon={faTimesCircle} className="text-red-500" />
      default: return null
    }
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <FontAwesomeIcon icon={faSpinner} spin className="text-2xl text-gray-400" />
        <span className="ml-2 text-gray-600">Loading API keys...</span>
      </div>
    )
  }

  return (
    <div className="api-keys-page p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <FontAwesomeIcon icon={faKey} className="mr-3 text-blue-600" />
            API Keys Management
          </h1>
          <p className="text-gray-600 mt-1">
            Manage service registrations and API access for ChatLo FileRoutingService
          </p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
        >
          <FontAwesomeIcon icon={faPlus} className="mr-2" />
          Create New API Key
        </button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white p-4 rounded-lg shadow border">
          <h3 className="text-sm font-medium text-gray-500">Total Services</h3>
          <p className="text-2xl font-bold text-gray-900">{apiKeys.length}</p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow border">
          <h3 className="text-sm font-medium text-gray-500">Active Services</h3>
          <p className="text-2xl font-bold text-green-600">
            {apiKeys.filter(k => k.status === 'active').length}
          </p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow border">
          <h3 className="text-sm font-medium text-gray-500">Total Requests</h3>
          <p className="text-2xl font-bold text-blue-600">
            {apiKeys.reduce((sum, k) => sum + k.totalRequests, 0).toLocaleString()}
          </p>
        </div>
        <div className="bg-white p-4 rounded-lg shadow border">
          <h3 className="text-sm font-medium text-gray-500">Avg Success Rate</h3>
          <p className="text-2xl font-bold text-purple-600">
            {apiKeys.length > 0 
              ? (apiKeys.reduce((sum, k) => sum + k.successRate, 0) / apiKeys.length).toFixed(1)
              : 0
            }%
          </p>
        </div>
      </div>

      {/* API Keys Table */}
      <div className="bg-white rounded-lg shadow border overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Registered Services</h2>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Service
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Developer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  API Key
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Capabilities
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Access
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Requests
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {apiKeys.map((apiKey) => (
                <tr key={apiKey.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{apiKey.name}</div>
                      <div className="text-sm text-gray-500">v{apiKey.version}</div>
                      <div className="text-xs text-gray-400">{apiKey.id}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm text-gray-900">{apiKey.developerInfo.name}</div>
                      <div className="text-sm text-gray-500">{apiKey.developerInfo.email}</div>
                      {apiKey.developerInfo.organization && (
                        <div className="text-xs text-gray-400">{apiKey.developerInfo.organization}</div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getStatusIcon(apiKey.status)}
                      <span className={`ml-2 text-sm capitalize ${
                        apiKey.status === 'active' ? 'text-green-600' :
                        apiKey.status === 'suspended' ? 'text-yellow-600' :
                        'text-red-600'
                      }`}>
                        {apiKey.status}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                        {visibleKeys.has(apiKey.id) ? apiKey.apiKey : '••••••••••••••••'}
                      </code>
                      <button
                        onClick={() => toggleKeyVisibility(apiKey.id)}
                        className="ml-2 text-gray-400 hover:text-gray-600"
                      >
                        <FontAwesomeIcon icon={visibleKeys.has(apiKey.id) ? faEyeSlash : faEye} />
                      </button>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex flex-wrap gap-1">
                      {apiKey.capabilities.map((cap) => (
                        <span
                          key={cap}
                          className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >
                          {cap}
                        </span>
                      ))}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {apiKey.lastAccess ? formatDate(apiKey.lastAccess) : 'Never'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{apiKey.totalRequests.toLocaleString()}</div>
                    <div className="text-sm text-gray-500">{apiKey.successRate.toFixed(1)}% success</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => copyToClipboard(apiKey.id)}
                        className="text-blue-600 hover:text-blue-900"
                        title="Copy Service ID"
                      >
                        <FontAwesomeIcon icon={faCopy} />
                      </button>
                      {apiKey.status === 'active' && (
                        <button
                          onClick={() => handleRevokeKey(apiKey.id, apiKey.name)}
                          className="text-red-600 hover:text-red-900"
                          title="Revoke Access"
                        >
                          <FontAwesomeIcon icon={faTrash} />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Create API Key Modal */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">Create New API Key</h3>
                <button
                  onClick={() => setShowCreateForm(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>

              <form onSubmit={(e) => { e.preventDefault(); handleCreateAPIKey(); }}>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Service Name *</label>
                    <input
                      type="text"
                      required
                      value={newService.name}
                      onChange={(e) => setNewService({...newService, name: e.target.value})}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="My File Processor"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Version *</label>
                    <input
                      type="text"
                      required
                      value={newService.version}
                      onChange={(e) => setNewService({...newService, version: e.target.value})}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="1.0.0"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Description</label>
                    <textarea
                      value={newService.description}
                      onChange={(e) => setNewService({...newService, description: e.target.value})}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      rows={3}
                      placeholder="Brief description of your service"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Developer Name *</label>
                    <input
                      type="text"
                      required
                      value={newService.developerName}
                      onChange={(e) => setNewService({...newService, developerName: e.target.value})}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="John Doe"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Developer Email *</label>
                    <input
                      type="email"
                      required
                      value={newService.developerEmail}
                      onChange={(e) => setNewService({...newService, developerEmail: e.target.value})}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">Organization</label>
                    <input
                      type="text"
                      value={newService.organization}
                      onChange={(e) => setNewService({...newService, organization: e.target.value})}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Acme Corporation"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Capabilities *</label>
                    <div className="space-y-2 max-h-32 overflow-y-auto">
                      {availableCapabilities.map((capability) => (
                        <label key={capability} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={newService.capabilities.includes(capability)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setNewService({
                                  ...newService,
                                  capabilities: [...newService.capabilities, capability]
                                })
                              } else {
                                setNewService({
                                  ...newService,
                                  capabilities: newService.capabilities.filter(c => c !== capability)
                                })
                              }
                            }}
                            className="mr-2"
                          />
                          <span className="text-sm text-gray-700">{capability}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    type="button"
                    onClick={() => setShowCreateForm(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={!newService.name || !newService.developerName || !newService.developerEmail || newService.capabilities.length === 0}
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 rounded-md"
                  >
                    Create API Key
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Created Key Display Modal */}
      {createdKey && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-center mb-4">
                <FontAwesomeIcon icon={faCheckCircle} className="text-green-500 text-3xl" />
              </div>

              <h3 className="text-lg font-medium text-gray-900 text-center mb-4">
                API Key Created Successfully!
              </h3>

              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
                <div className="flex">
                  <FontAwesomeIcon icon={faExclamationTriangle} className="text-yellow-400 mt-0.5 mr-2" />
                  <div className="text-sm text-yellow-700">
                    <strong>Important:</strong> This is the only time you'll see your API key.
                    Please copy and store it securely.
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Service ID</label>
                  <div className="mt-1 flex">
                    <code className="flex-1 bg-gray-100 px-3 py-2 rounded-l-md text-sm">
                      {createdKey.serviceId}
                    </code>
                    <button
                      onClick={() => copyToClipboard(createdKey.serviceId)}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-r-md"
                    >
                      <FontAwesomeIcon icon={faCopy} />
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">API Key</label>
                  <div className="mt-1 flex">
                    <code className="flex-1 bg-gray-100 px-3 py-2 rounded-l-md text-sm">
                      {createdKey.apiKey}
                    </code>
                    <button
                      onClick={() => copyToClipboard(createdKey.apiKey)}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-r-md"
                    >
                      <FontAwesomeIcon icon={faCopy} />
                    </button>
                  </div>
                </div>
              </div>

              <div className="mt-6 text-center">
                <button
                  onClick={() => setCreatedKey(null)}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md"
                >
                  I've Saved My Credentials
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
