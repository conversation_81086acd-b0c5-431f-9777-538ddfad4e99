import React, { useState, useEffect } from 'react'
import { useAppStore } from '../store'
import { ArrowLeft, Key, Settings, User, FileText, Folder, RefreshCw, Sliders } from '../components/Icons'
import { useNavigate } from 'react-router-dom'

import { contextVaultService } from '../services/contextVaultService'
import { sharedDropboxService } from '../services/sharedDropboxService'
import { IntelligenceTestLauncher } from '../components/IntelligenceTestLauncher'
import { IntelligenceAnalyticsDashboard } from '../components/IntelligenceAnalyticsDashboard'
import { PluginManager } from '../components/PluginManager'
import { VaultMigrationSettings } from '../components/VaultMigrationSettings'
import FileProcessingDiagnostics from '../components/FileProcessingDiagnostics'

const SettingsPage: React.FC = () => {
  const navigate = useNavigate()
  const { settings, updateSettings } = useAppStore()
  const [activeTab, setActiveTab] = useState('api')
  const [localSettings, setLocalSettings] = useState(settings)
  const [isLoading, setIsLoading] = useState(false)
  const [testResult, setTestResult] = useState<string | null>(null)

  const [storageInfo, setStorageInfo] = useState<any>(null)



  // Diagnostics state
  const [showDiagnostics, setShowDiagnostics] = useState(false)

  useEffect(() => {
    setLocalSettings(settings)
    loadStorageInfo()
  }, [settings])



  const loadStorageInfo = async () => {
    try {
      if (window.electronAPI?.files) {
        const files = await window.electronAPI.files.getIndexedFiles()
        const totalSize = files.reduce((sum, file) => sum + file.file_size, 0)
        setStorageInfo({
          totalFiles: files.length,
          totalSize: totalSize,
          fileTypes: files.reduce((acc, file) => {
            acc[file.file_type] = (acc[file.file_type] || 0) + 1
            return acc
          }, {} as Record<string, number>)
        })
      }
    } catch (error) {
      console.error('Error loading storage info:', error)
    }
  }



  // Refresh services after vault root path change
  const refreshServicesAfterVaultChange = async () => {
    try {
      // Refresh the context vault service to load data from new location
      await contextVaultService.refreshVaults()
      console.log('Context vault service refreshed after vault path change')

      // Reinitialize shared dropbox service for new vault location
      await sharedDropboxService.initialize()
      console.log('Shared dropbox service reinitialized after vault path change')
    } catch (error) {
      console.error('Error refreshing services after vault change:', error)
    }
  }





  const handleSave = async () => {
    try {
      setIsLoading(true)
      updateSettings(localSettings)

      if (window.electronAPI?.settings) {
        await window.electronAPI.settings.set('app-settings', localSettings)
      }

      setTestResult('✅ Settings saved successfully!')
      setTimeout(() => setTestResult(null), 3000)
    } catch (error) {
      console.error('Error saving settings:', error)
      setTestResult('❌ Failed to save settings')
      setTimeout(() => setTestResult(null), 3000)
    } finally {
      setIsLoading(false)
    }
  }

  const testApiKey = async () => {
    if (!localSettings.openRouterApiKey) {
      setTestResult('❌ Please enter an API key first')
      return
    }

    try {
      setIsLoading(true)
      setTestResult('Testing API key...')

      // Import the service dynamically to avoid circular dependencies
      const { openRouterService } = await import('../services/openrouter')
      openRouterService.setApiKey(localSettings.openRouterApiKey)

      const validation = await openRouterService.validateApiKey()

      if (validation.valid) {
        setTestResult('✅ API key is valid!')
      } else {
        setTestResult('❌ Invalid API key')
      }
    } catch (error) {
      console.error('Error testing API key:', error)
      setTestResult('❌ Failed to test API key')
    } finally {
      setIsLoading(false)
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }



  const tabs = [
    { id: 'api', label: 'API Setup', icon: Key },
    { id: 'data', label: 'Data Management', icon: FileText },
    { id: 'plugins', label: 'Plugins', icon: Sliders },
    { id: 'profile', label: 'User Profile', icon: User },
    { id: 'test', label: 'Intelligence Test', icon: Settings },
    { id: 'diagnostics', label: 'File Diagnostics', icon: RefreshCw },
  ]

  return (
    <div className="h-screen flex flex-col bg-gray-900 text-supplement1">
      {/* Header */}
      <header className="flex items-center gap-4 h-16 px-6 border-b border-tertiary bg-gray-800/60 backdrop-blur-lg">
        <button
          onClick={() => navigate('/')}
          className="u1-button-ghost h-8 w-8 flex items-center justify-center"
        >
          <ArrowLeft className="h-4 w-4" />
        </button>
        <div className="flex items-center gap-2">
          <Settings className="h-5 w-5 text-primary" />
          <h1 className="text-xl font-semibold text-supplement1">Settings</h1>
        </div>
      </header>

      <div className="flex-1 flex">
        {/* Sidebar */}
        <nav className="w-64 border-r border-tertiary bg-gray-800/30">
          <div className="p-4">
            <div className="space-y-1">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`
                      w-full flex items-center gap-3 px-3 py-2 rounded-lg text-sm transition-colors
                      ${activeTab === tab.id
                        ? 'bg-primary text-gray-900'
                        : 'text-gray-400 hover:text-supplement1 hover:bg-gray-700'
                      }
                    `}
                  >
                    <Icon className="h-4 w-4" />
                    {tab.label}
                  </button>
                )
              })}
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto">
          <div className="max-w-4xl mx-auto p-8">
            {activeTab === 'api' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2 text-supplement1">API Configuration</h2>
                  <p className="text-gray-400">Configure your OpenRouter API key to access AI models.</p>
                </div>

                <div className="u1-card bg-gray-800/50 border border-gray-700">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2 text-supplement1">
                        OpenRouter API Key
                      </label>
                      <div className="flex gap-3">
                        <input
                          type="password"
                          value={localSettings.openRouterApiKey || ''}
                          onChange={(e) => setLocalSettings(prev => ({
                            ...prev,
                            openRouterApiKey: e.target.value
                          }))}
                          placeholder="sk-or-..."
                          className="u1-input-field flex-1"
                        />
                        <button
                          onClick={testApiKey}
                          disabled={isLoading}
                          className="u1-button-ghost px-4 py-2 disabled:opacity-50"
                        >
                          Test
                        </button>
                      </div>
                      {testResult && (
                        <p className="mt-2 text-sm">{testResult}</p>
                      )}
                    </div>

                    <div className="flex gap-3">
                      <button
                        onClick={handleSave}
                        disabled={isLoading}
                        className="u1-button-primary disabled:opacity-50"
                      >
                        Save Settings
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'data' && (
              <div className="space-y-6">
                {/* New Vault Migration Settings */}
                <VaultMigrationSettings
                  onVaultCreated={() => {
                    // Refresh services after vault creation
                    refreshServicesAfterVaultChange()
                  }}
                />

                {/* File Storage Stats */}
                {storageInfo && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="u1-card bg-gray-800/50 border border-gray-700">
                      <h3 className="text-lg font-medium mb-4">File Storage</h3>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="flex justify-between">
                          <span className="text-neutral-400">Total Files:</span>
                          <span className="text-neutral-200">{storageInfo.totalFiles}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-neutral-400">Total Size:</span>
                          <span className="text-neutral-200">{formatFileSize(storageInfo.totalSize)}</span>
                        </div>
                      </div>
                    </div>

                    {/* File Types */}
                    <div className="bg-neutral-900/50 border border-neutral-800 rounded-lg p-6">
                      <h3 className="text-lg font-medium mb-4">File Types</h3>
                      <div className="space-y-2">
                        {Object.entries(storageInfo.fileTypes).map(([type, count]) => (
                          <div key={type} className="flex justify-between text-sm">
                            <span className="text-neutral-400 capitalize">{type}:</span>
                            <span className="text-neutral-200">{String(count)}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'profile' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2">User Profile</h2>
                  <p className="text-neutral-400">Manage your profile and preferences.</p>
                </div>

                <div className="bg-neutral-900/50 border border-neutral-800 rounded-lg p-6">
                  <div className="text-center py-8">
                    <User className="h-12 w-12 text-neutral-500 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-neutral-400 mb-2">Coming Soon</h3>
                    <p className="text-sm text-neutral-500">
                      User profile features will be available in a future update.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'plugins' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2 text-supplement1">Plugin Management</h2>
                  <p className="text-gray-400">Manage your plugins and extensions.</p>
                </div>

                <PluginManager />
              </div>
            )}

            {activeTab === 'test' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2 text-supplement1">Intelligence System Test</h2>
                  <p className="text-gray-400">Test the intelligence extraction and performance monitoring systems.</p>
                </div>

                <IntelligenceTestLauncher />

                <div className="border-t border-tertiary/30 pt-6">
                  <h3 className="text-xl font-semibold mb-4 text-supplement1">Analytics Dashboard</h3>
                  <IntelligenceAnalyticsDashboard />
                </div>
              </div>
            )}

            {activeTab === 'diagnostics' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2 text-supplement1">File Processing Diagnostics</h2>
                  <p className="text-gray-400">Diagnose file processing issues and troubleshoot PDF parsing problems.</p>
                </div>

                <div className="bg-neutral-800/50 rounded-lg p-6 border border-neutral-600">
                  <h3 className="text-lg font-semibold mb-4 text-white">Diagnostic Tool</h3>
                  <p className="text-neutral-300 mb-4">
                    Use this tool to diagnose file processing issues. It will check file system access,
                    plugin availability, content processing, and database integration.
                  </p>
                  <button
                    onClick={() => setShowDiagnostics(true)}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center gap-2"
                  >
                    <RefreshCw className="w-4 h-4" />
                    Open Diagnostics Tool
                  </button>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>

      {/* File Processing Diagnostics Modal */}
      <FileProcessingDiagnostics
        isOpen={showDiagnostics}
        onClose={() => setShowDiagnostics(false)}
      />
    </div>
  )
}

export default SettingsPage
